#!/usr/bin/env python3
"""
Debug script to test mouse control in scrcpy window.
This will help us understand why the cursor isn't moving.
"""

import time
import pyauto<PERSON><PERSON>

def test_mouse_movement():
    """Test basic mouse movement to debug the issue."""
    
    print("=== Mouse Movement Debug Test ===")
    print()
    
    # Disable PyAutoGUI failsafe
    pyautogui.FAILSAFE = False
    
    print("Current mouse position:", pyautogui.position())
    print("Screen size:", pyautogui.size())
    print()
    
    print("This test will:")
    print("1. Move the mouse to different positions on your screen")
    print("2. Test clicking and dragging")
    print("3. Help identify if PyAutoGUI works with scrcpy")
    print()
    
    input("Make sure scrcpy window is open and visible. Press Enter to start...")
    
    # Get current mouse position
    start_x, start_y = pyautogui.position()
    print(f"Starting position: ({start_x}, {start_y})")
    
    print("Test 1: Moving mouse to screen center...")
    screen_width, screen_height = pyautogui.size()
    center_x, center_y = screen_width // 2, screen_height // 2
    pyautogui.moveTo(center_x, center_y, duration=1)
    time.sleep(1)
    
    print("Test 2: Drawing a small square...")
    square_size = 100
    # Move to top-left of square
    pyautogui.moveTo(center_x - square_size//2, center_y - square_size//2, duration=0.5)
    time.sleep(0.5)
    
    # Draw square with mouse down
    pyautogui.mouseDown()
    pyautogui.dragTo(center_x + square_size//2, center_y - square_size//2, duration=0.5)  # Top edge
    pyautogui.dragTo(center_x + square_size//2, center_y + square_size//2, duration=0.5)  # Right edge
    pyautogui.dragTo(center_x - square_size//2, center_y + square_size//2, duration=0.5)  # Bottom edge
    pyautogui.dragTo(center_x - square_size//2, center_y - square_size//2, duration=0.5)  # Left edge
    pyautogui.mouseUp()
    
    print("Test 3: Single click test...")
    time.sleep(1)
    pyautogui.click(center_x, center_y)
    
    print("Test 4: Return to starting position...")
    pyautogui.moveTo(start_x, start_y, duration=1)
    
    print()
    print("Debug test completed!")
    print("Did you see the mouse cursor move during this test?")
    print("If yes, then PyAutoGUI works and we need to focus on the scrcpy window.")
    print("If no, then there's an issue with PyAutoGUI on your system.")

if __name__ == "__main__":
    try:
        test_mouse_movement()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed with error: {e}")
