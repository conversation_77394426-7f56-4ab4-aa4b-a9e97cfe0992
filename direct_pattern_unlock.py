#!/usr/bin/env python3
"""
Direct pattern unlock for scrcpy --otg mode.
Since the cursor appears directly on the phone, we use device coordinates directly.
"""

import argparse
import subprocess
import time
import sys
from typing import List, Tuple, Optional

# Try to import pyautogui
try:
    import pyautogui
    PYAUTOGUI_AVAILABLE = True
    # Disable failsafe for automation
    pyautogui.FAILSAFE = False
except ImportError:
    PYAUTOGUI_AVAILABLE = False
    print("PyAutoGUI not available. Please install with: pip install pyautogui")

class DirectPatternUnlocker:
    """Direct pattern unlocker that works with device coordinates."""
    
    def __init__(self, resolution: str, scrcpy_path: str = "scrcpy"):
        """Initialize the pattern unlocker."""
        self.scrcpy_path = scrcpy_path
        self.width, self.height = self._parse_resolution(resolution)
        self.scrcpy_process: Optional[subprocess.Popen] = None
        
        # Standard Android pattern grid positions (3x3 grid)
        self.pattern_grid = self._calculate_pattern_grid()
    
    def _parse_resolution(self, resolution: str) -> Tuple[int, int]:
        """Parse resolution string into width and height."""
        try:
            width, height = map(int, resolution.split('x'))
            return width, height
        except ValueError:
            raise ValueError(f"Invalid resolution format: {resolution}. Use WIDTHxHEIGHT format.")
    
    def _calculate_pattern_grid(self) -> List[List[Tuple[int, int]]]:
        """Calculate the 3x3 pattern grid coordinates based on screen resolution."""
        # Pattern grid ratios for Samsung devices
        grid_start_x_ratio = 0.2    # 20% from left edge
        grid_end_x_ratio = 0.8      # 80% from left edge
        grid_start_y_ratio = 0.4    # 40% from top edge  
        grid_end_y_ratio = 0.75     # 75% from top edge
        
        grid_start_x = int(self.width * grid_start_x_ratio)
        grid_end_x = int(self.width * grid_end_x_ratio)
        grid_start_y = int(self.height * grid_start_y_ratio)
        grid_end_y = int(self.height * grid_end_y_ratio)
        
        # Calculate 3x3 grid positions
        grid = []
        for row in range(3):
            grid_row = []
            for col in range(3):
                x = grid_start_x + (grid_end_x - grid_start_x) * col // 2
                y = grid_start_y + (grid_end_y - grid_start_y) * row // 2
                grid_row.append((x, y))
            grid.append(grid_row)
        
        return grid
    
    def get_pattern_coordinates(self, pattern: str) -> List[Tuple[int, int]]:
        """Convert pattern string to list of device coordinates."""
        try:
            pattern_numbers = [int(x.strip()) for x in pattern.split(',')]
        except ValueError:
            raise ValueError(f"Invalid pattern format: {pattern}. Use comma-separated numbers 1-9.")
        
        coordinates = []
        for num in pattern_numbers:
            if num < 1 or num > 9:
                raise ValueError(f"Pattern number {num} out of range. Use numbers 1-9.")
            
            # Convert number to grid position (1-9 maps to 3x3 grid)
            row = (num - 1) // 3
            col = (num - 1) % 3
            coordinates.append(self.pattern_grid[row][col])
        
        return coordinates
    
    def start_scrcpy_otg(self) -> bool:
        """Start scrcpy in OTG mode."""
        try:
            print("Starting scrcpy in OTG mode...")
            cmd = [self.scrcpy_path, "--otg"]
            
            self.scrcpy_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give scrcpy time to start
            time.sleep(3)
            
            # Check if process is still running
            if self.scrcpy_process.poll() is not None:
                stdout, stderr = self.scrcpy_process.communicate()
                print("Failed to start scrcpy OTG mode")
                print(f"Error output: {stderr}")
                return False
            
            print("✓ scrcpy OTG mode started successfully")
            print("You should see a cursor appear on your phone screen")
            return True
            
        except Exception as e:
            print(f"Error starting scrcpy: {e}")
            return False
    
    def draw_pattern_directly(self, coordinates: List[Tuple[int, int]], duration: float = 2.0) -> bool:
        """
        Draw the pattern using direct mouse movements.
        Since scrcpy --otg shows cursor on device, we use absolute positioning.
        """
        if not PYAUTOGUI_AVAILABLE:
            print("PyAutoGUI not available for mouse control")
            return False
        
        if len(coordinates) < 2:
            print("Need at least 2 points to draw a pattern")
            return False
        
        try:
            print(f"Drawing pattern through {len(coordinates)} points...")
            print("Note: The cursor will move on your phone screen directly")
            
            # Get current mouse position to return to later
            original_pos = pyautogui.position()
            
            # Since scrcpy --otg maps device coordinates directly to mouse movement,
            # we need to use relative movements or find the mapping
            
            # For now, let's use a simple approach: move to each coordinate
            # The exact mapping may need adjustment based on how scrcpy --otg works
            
            # Move to first point
            start_x, start_y = coordinates[0]
            print(f"Moving to start position: ({start_x}, {start_y})")
            
            # Use relative movements from current position
            # This is experimental - may need adjustment
            current_x, current_y = pyautogui.position()
            
            # Calculate relative movement needed
            # This assumes a 1:1 mapping which may not be correct
            rel_x = start_x - self.width // 2
            rel_y = start_y - self.height // 2
            
            # Move to starting position
            pyautogui.moveRel(rel_x, rel_y, duration=0.5)
            time.sleep(0.3)
            
            # Press down to start drawing
            pyautogui.mouseDown()
            print("Started drawing pattern...")
            
            # Draw lines to each subsequent point
            segment_duration = duration / (len(coordinates) - 1)
            
            for i in range(1, len(coordinates)):
                end_x, end_y = coordinates[i]
                prev_x, prev_y = coordinates[i-1]
                
                # Calculate relative movement
                rel_x = end_x - prev_x
                rel_y = end_y - prev_y
                
                print(f"Drawing to point {i+1}: relative move ({rel_x}, {rel_y})")
                pyautogui.moveRel(rel_x, rel_y, duration=segment_duration)
                time.sleep(0.1)
            
            # Release mouse
            pyautogui.mouseUp()
            print("✓ Pattern drawing completed!")
            
            # Return mouse to original position
            pyautogui.moveTo(original_pos.x, original_pos.y, duration=0.5)
            
            return True
            
        except Exception as e:
            print(f"Error drawing pattern: {e}")
            return False
    
    def unlock_pattern(self, pattern: str, swipe_duration: float = 2.0) -> bool:
        """Perform the unlock pattern."""
        try:
            print(f"\nPreparing to draw unlock pattern: {pattern}")
            
            # Get device coordinates for the pattern
            device_coords = self.get_pattern_coordinates(pattern)
            print(f"Pattern coordinates: {device_coords}")
            
            # Wait for user confirmation
            print("\nIMPORTANT:")
            print("- Make sure your phone screen is showing the pattern lock")
            print("- The cursor should be visible on your phone screen")
            print("- Don't touch your phone during pattern drawing")
            
            input("Press Enter when ready to draw the pattern...")
            
            # Draw the pattern
            success = self.draw_pattern_directly(device_coords, swipe_duration)
            
            if success:
                print("\n✓ Pattern unlock completed!")
                print("Check if your phone unlocked.")
                return True
            else:
                print("\n✗ Pattern unlock failed!")
                return False
                
        except Exception as e:
            print(f"Error during pattern unlock: {e}")
            return False
    
    def cleanup(self) -> None:
        """Clean up resources."""
        if self.scrcpy_process and self.scrcpy_process.poll() is None:
            print("Terminating scrcpy process...")
            self.scrcpy_process.terminate()
            try:
                self.scrcpy_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("Force killing scrcpy process...")
                self.scrcpy_process.kill()
            self.scrcpy_process = None

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Direct Samsung pattern unlock via scrcpy --otg")
    parser.add_argument("--resolution", required=True, help="Screen resolution (e.g., 1080x2340)")
    parser.add_argument("--pattern", required=True, help="Unlock pattern (e.g., 1,2,5,6,9)")
    parser.add_argument("--scrcpy-path", default="scrcpy", help="Path to scrcpy executable")
    parser.add_argument("--swipe-duration", type=float, default=2.0, help="Duration for pattern drawing")
    
    args = parser.parse_args()
    
    if not PYAUTOGUI_AVAILABLE:
        print("Error: PyAutoGUI is required. Install with: pip install pyautogui")
        return 1
    
    unlocker = DirectPatternUnlocker(args.resolution, args.scrcpy_path)
    
    try:
        # Start scrcpy
        if not unlocker.start_scrcpy_otg():
            print("Failed to start scrcpy")
            return 1
        
        # Perform unlock
        success = unlocker.unlock_pattern(args.pattern, args.swipe_duration)
        
        return 0 if success else 1
            
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1
    finally:
        unlocker.cleanup()

if __name__ == "__main__":
    sys.exit(main())
