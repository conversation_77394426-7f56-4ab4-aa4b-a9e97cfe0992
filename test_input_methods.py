#!/usr/bin/env python3
"""
Test script to try different input methods with scrcpy --otg in sequence.
This will help identify which method actually works for sending input to the phone.
"""

import subprocess
import time
import sys
from typing import List, Tuple, Optional

class InputMethodTester:
    """Test different input methods with scrcpy --otg."""
    
    def __init__(self, scrcpy_path: str = "scrcpy"):
        """Initialize the tester."""
        self.scrcpy_path = scrcpy_path
        self.scrcpy_process: Optional[subprocess.Popen] = None
        
        # Test coordinates - simple pattern: 1 -> 2 (horizontal line)
        self.test_coords = [
            (216, 936),   # Point 1 (top-left of pattern grid)
            (540, 936),   # Point 2 (top-center of pattern grid)
        ]
        
        print("Input Method Tester for scrcpy --otg")
        print("=" * 45)
        print(f"Test pattern: Simple line from point 1 to point 2")
        print(f"Coordinates: {self.test_coords}")
        print()
    
    def start_scrcpy_otg(self) -> bool:
        """Start scrcpy in OTG mode."""
        try:
            print("🚀 Starting scrcpy in OTG mode...")
            cmd = [self.scrcpy_path, "--otg"]
            
            self.scrcpy_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give scrcpy time to start
            time.sleep(3)
            
            # Check if process is still running
            if self.scrcpy_process.poll() is not None:
                stdout, stderr = self.scrcpy_process.communicate()
                print("❌ Failed to start scrcpy OTG mode")
                print(f"Error output: {stderr}")
                return False
            
            print("✅ scrcpy OTG mode started successfully")
            print("📱 You should see a cursor appear on your phone screen")
            print()
            return True
            
        except Exception as e:
            print(f"❌ Error starting scrcpy: {e}")
            return False
    
    def countdown(self, seconds: int, message: str = ""):
        """Display countdown with optional message."""
        if message:
            print(f"⏳ {message}")
        for i in range(seconds, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
        print("   GO!")
    
    def test_cliclick_drag(self) -> bool:
        """Test Method 1: cliclick drag."""
        print("🧪 TEST 1: cliclick drag method")
        print("   This uses mouse down -> drag -> mouse up")
        
        try:
            # Check if cliclick is available
            subprocess.run(['which', 'cliclick'], check=True, capture_output=True)
            
            self.countdown(3, "Starting cliclick drag test...")
            
            start_x, start_y = self.test_coords[0]
            end_x, end_y = self.test_coords[1]
            
            print(f"   Moving to start: ({start_x}, {start_y})")
            subprocess.run(['cliclick', f'm:{start_x},{start_y}'], check=True)
            time.sleep(0.3)
            
            print(f"   Mouse down and drag to: ({end_x}, {end_y})")
            subprocess.run(['cliclick', 'dd:.'], check=True)
            time.sleep(0.2)
            subprocess.run(['cliclick', f'm:{end_x},{end_y}'], check=True)
            time.sleep(1.0)
            subprocess.run(['cliclick', 'du:.'], check=True)
            
            print("✅ cliclick drag test completed")
            return True
            
        except subprocess.CalledProcessError:
            print("❌ cliclick not available or command failed")
            return False
        except Exception as e:
            print(f"❌ cliclick drag test failed: {e}")
            return False
    
    def test_cliclick_clicks(self) -> bool:
        """Test Method 2: cliclick individual clicks."""
        print("\n🧪 TEST 2: cliclick individual clicks")
        print("   This clicks each point separately")
        
        try:
            self.countdown(3, "Starting cliclick clicks test...")
            
            for i, (x, y) in enumerate(self.test_coords):
                print(f"   Clicking point {i+1}: ({x}, {y})")
                subprocess.run(['cliclick', f'c:{x},{y}'], check=True)
                time.sleep(0.8)
            
            print("✅ cliclick clicks test completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ cliclick clicks test failed: {e}")
            return False
        except Exception as e:
            print(f"❌ cliclick clicks test failed: {e}")
            return False
    
    def test_pyautogui_drag(self) -> bool:
        """Test Method 3: PyAutoGUI drag."""
        print("\n🧪 TEST 3: PyAutoGUI drag method")
        print("   This uses PyAutoGUI mouse automation")
        
        try:
            import pyautogui
            
            self.countdown(3, "Starting PyAutoGUI drag test...")
            
            start_x, start_y = self.test_coords[0]
            end_x, end_y = self.test_coords[1]
            
            print(f"   PyAutoGUI drag from ({start_x}, {start_y}) to ({end_x}, {end_y})")
            pyautogui.drag(end_x - start_x, end_y - start_y, duration=1.5, button='left')
            
            print("✅ PyAutoGUI drag test completed")
            return True
            
        except ImportError:
            print("❌ PyAutoGUI not available (pip install pyautogui)")
            return False
        except Exception as e:
            print(f"❌ PyAutoGUI drag test failed: {e}")
            return False
    
    def test_pyautogui_clicks(self) -> bool:
        """Test Method 4: PyAutoGUI clicks."""
        print("\n🧪 TEST 4: PyAutoGUI individual clicks")
        print("   This clicks each point with PyAutoGUI")
        
        try:
            import pyautogui
            
            self.countdown(3, "Starting PyAutoGUI clicks test...")
            
            for i, (x, y) in enumerate(self.test_coords):
                print(f"   PyAutoGUI click point {i+1}: ({x}, {y})")
                pyautogui.click(x, y)
                time.sleep(0.8)
            
            print("✅ PyAutoGUI clicks test completed")
            return True
            
        except ImportError:
            print("❌ PyAutoGUI not available")
            return False
        except Exception as e:
            print(f"❌ PyAutoGUI clicks test failed: {e}")
            return False
    
    def test_applescript_drag(self) -> bool:
        """Test Method 5: AppleScript drag."""
        print("\n🧪 TEST 5: AppleScript drag method")
        print("   This uses macOS AppleScript for mouse control")
        
        try:
            self.countdown(3, "Starting AppleScript drag test...")
            
            start_x, start_y = self.test_coords[0]
            end_x, end_y = self.test_coords[1]
            
            applescript = f'''
            tell application "System Events"
                set startPos to {{{start_x}, {start_y}}}
                set endPos to {{{end_x}, {end_y}}}
                
                -- Move to start position
                set the mouse location to startPos
                delay 0.3
                
                -- Click and drag
                mouse down at startPos
                delay 0.2
                set the mouse location to endPos
                delay 1.0
                mouse up at endPos
            end tell
            '''
            
            print(f"   AppleScript drag from ({start_x}, {start_y}) to ({end_x}, {end_y})")
            subprocess.run(['osascript', '-e', applescript], check=True)
            
            print("✅ AppleScript drag test completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ AppleScript drag test failed: {e}")
            return False
        except Exception as e:
            print(f"❌ AppleScript drag test failed: {e}")
            return False
    
    def test_applescript_clicks(self) -> bool:
        """Test Method 6: AppleScript clicks."""
        print("\n🧪 TEST 6: AppleScript individual clicks")
        print("   This clicks each point with AppleScript")
        
        try:
            self.countdown(3, "Starting AppleScript clicks test...")
            
            for i, (x, y) in enumerate(self.test_coords):
                applescript = f'''
                tell application "System Events"
                    click at {{{x}, {y}}}
                end tell
                '''
                
                print(f"   AppleScript click point {i+1}: ({x}, {y})")
                subprocess.run(['osascript', '-e', applescript], check=True)
                time.sleep(0.8)
            
            print("✅ AppleScript clicks test completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ AppleScript clicks test failed: {e}")
            return False
        except Exception as e:
            print(f"❌ AppleScript clicks test failed: {e}")
            return False
    
    def run_all_tests(self) -> None:
        """Run all input method tests in sequence."""
        print("📋 INSTRUCTIONS:")
        print("   1. Make sure your phone shows the pattern lock screen")
        print("   2. Watch your phone screen during each test")
        print("   3. Note which method (if any) moves the cursor or draws lines")
        print("   4. Each test will have a 3-second countdown")
        print()

        print("🚀 Starting tests automatically in 5 seconds...")
        for i in range(5, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
        print("   Starting tests now!")
        print()
        
        # List of test methods
        test_methods = [
            ("cliclick drag", self.test_cliclick_drag),
            ("cliclick clicks", self.test_cliclick_clicks),
            ("PyAutoGUI drag", self.test_pyautogui_drag),
            ("PyAutoGUI clicks", self.test_pyautogui_clicks),
            ("AppleScript drag", self.test_applescript_drag),
            ("AppleScript clicks", self.test_applescript_clicks),
        ]
        
        results = {}
        
        for method_name, test_func in test_methods:
            try:
                success = test_func()
                results[method_name] = "✅ Completed" if success else "❌ Failed"
                
                # Pause between tests
                if method_name != test_methods[-1][0]:  # Not the last test
                    print(f"\n⏸️  Pausing 2 seconds before next test...")
                    time.sleep(2)
                    
            except KeyboardInterrupt:
                print(f"\n⏹️  Test interrupted during: {method_name}")
                break
            except Exception as e:
                results[method_name] = f"❌ Error: {e}"
        
        # Display results summary
        print("\n" + "=" * 50)
        print("📊 TEST RESULTS SUMMARY")
        print("=" * 50)
        
        for method_name, result in results.items():
            print(f"{method_name:20} : {result}")
        
        print("\n❓ QUESTIONS FOR YOU:")
        print("   - Did you see the cursor move on your phone during any test?")
        print("   - Did any test draw a line or show visual feedback?")
        print("   - Which method(s) seemed to have any effect?")
        print()
    
    def cleanup(self) -> None:
        """Clean up resources."""
        if self.scrcpy_process and self.scrcpy_process.poll() is None:
            print("🧹 Terminating scrcpy process...")
            self.scrcpy_process.terminate()
            try:
                self.scrcpy_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("🔨 Force killing scrcpy process...")
                self.scrcpy_process.kill()
            self.scrcpy_process = None

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test input methods with scrcpy --otg")
    parser.add_argument("--scrcpy-path", default="/Users/<USER>/Downloads/scrcpy-macos-aarch64-v3.3.1/scrcpy", 
                       help="Path to scrcpy executable")
    
    args = parser.parse_args()
    
    tester = InputMethodTester(args.scrcpy_path)
    
    try:
        # Start scrcpy
        if not tester.start_scrcpy_otg():
            print("❌ Failed to start scrcpy")
            return 1
        
        # Run all tests
        tester.run_all_tests()
        
        return 0
            
    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    finally:
        tester.cleanup()

if __name__ == "__main__":
    sys.exit(main())
