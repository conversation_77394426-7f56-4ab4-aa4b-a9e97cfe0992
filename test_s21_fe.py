#!/usr/bin/env python3
"""
Test script for Samsung Galaxy S21 FE with pattern 1,2,5,6,9

This script shows exactly what commands would be sent to your S21 FE
and compares the coordinates with the Galaxy Tab A for reference.
"""

from pattern_unlock import PatternUnlocker


def test_s21_fe_pattern():
    """Test the pattern unlock for Samsung Galaxy S21 FE."""
    
    # S21 FE specifications
    S21_FE_RESOLUTION = "1080x2340"  # Galaxy S21 FE
    TAB_A_RESOLUTION = "800x1280"    # Galaxy Tab A 8-inch (for comparison)
    PATTERN = "1,2,5,6,9"            # Your unlock pattern
    
    print("Samsung Galaxy S21 FE vs Galaxy Tab A Pattern Test")
    print("=" * 55)
    print(f"S21 FE Resolution: {S21_FE_RESOLUTION}")
    print(f"Tab A Resolution:  {TAB_A_RESOLUTION}")
    print(f"Pattern: {PATTERN}")
    print()
    
    # Create unlocker instances for both devices
    s21_fe_unlocker = PatternUnlocker(S21_FE_RESOLUTION)
    tab_a_unlocker = PatternUnlocker(TAB_A_RESOLUTION)
    
    # Get pattern coordinates for both devices
    s21_fe_coords = s21_fe_unlocker.get_pattern_coordinates(PATTERN)
    tab_a_coords = tab_a_unlocker.get_pattern_coordinates(PATTERN)
    
    print("Pattern Grid Layout:")
    print("1 2 3")
    print("4 5 6")
    print("7 8 9")
    print()
    
    print("Your Pattern Visualization:")
    print("1 2 .")
    print(". 3 4")
    print(". . 5")
    print()
    
    print("Touch Coordinates Comparison:")
    print("Point | S21 FE (1080x2340)    | Tab A (800x1280)     | Difference")
    print("------|----------------------|---------------------|------------")
    
    pattern_points = PATTERN.split(',')
    for i, (s21_coord, tab_coord) in enumerate(zip(s21_fe_coords, tab_a_coords)):
        point_num = pattern_points[i]
        s21_x, s21_y = s21_coord
        tab_x, tab_y = tab_coord
        diff_x = s21_x - tab_x
        diff_y = s21_y - tab_y
        print(f"  {point_num}   | ({s21_x:4d}, {s21_y:4d})        | ({tab_x:3d}, {tab_y:3d})        | (+{diff_x:3d}, +{diff_y:3d})")
    
    print()
    print("Pattern Path:")
    print("  1 → 2 → 5 → 6 → 9")
    
    # Format S21 FE coordinates nicely
    coord_str = " → ".join([f"({x},{y})" for x, y in s21_fe_coords])
    print(f"  S21 FE: {coord_str}")
    
    coord_str = " → ".join([f"({x},{y})" for x, y in tab_a_coords])
    print(f"  Tab A:  {coord_str}")
    print()
    
    print("Commands that would be sent to S21 FE via scrcpy:")
    print("1. key POWER                    # Wake screen")
    for i, (x, y) in enumerate(s21_fe_coords):
        if i == 0:
            print(f"2. touch {x} {y} down          # Touch point {pattern_points[i]}")
        else:
            print(f"{i+2}. touch {x} {y} move          # Swipe to point {pattern_points[i]}")
    print(f"{len(s21_fe_coords)+2}. touch {s21_fe_coords[-1][0]} {s21_fe_coords[-1][1]} up            # Release touch")
    print()
    
    # Calculate pattern area info for S21 FE
    print("S21 FE Pattern Area Analysis:")
    print(f"  Screen size: {s21_fe_unlocker.width} x {s21_fe_unlocker.height}")
    
    # Get pattern bounds
    min_x = min(coord[0] for coord in s21_fe_coords)
    max_x = max(coord[0] for coord in s21_fe_coords)
    min_y = min(coord[1] for coord in s21_fe_coords)
    max_y = max(coord[1] for coord in s21_fe_coords)
    
    print(f"  Pattern area: {min_x}-{max_x} x {min_y}-{max_y}")
    print(f"  Pattern width: {max_x - min_x} pixels")
    print(f"  Pattern height: {max_y - min_y} pixels")
    print(f"  Pattern center: ({(min_x + max_x) // 2}, {(min_y + max_y) // 2})")
    
    # Calculate scaling factors
    scale_x = s21_fe_unlocker.width / tab_a_unlocker.width
    scale_y = s21_fe_unlocker.height / tab_a_unlocker.height
    
    print()
    print("Scaling Analysis:")
    print(f"  Width scaling:  {scale_x:.2f}x (S21 FE is {scale_x:.1f}x wider)")
    print(f"  Height scaling: {scale_y:.2f}x (S21 FE is {scale_y:.1f}x taller)")
    
    return s21_fe_coords


def show_s21_fe_usage():
    """Show usage instructions for S21 FE."""
    
    print("\nSamsung Galaxy S21 FE Usage Instructions")
    print("=" * 42)
    print()
    print("1. Connect your Galaxy S21 FE via USB")
    print("2. Make sure the phone screen is off or showing lock screen")
    print("3. Run the unlock command:")
    print()
    print('   python pattern_unlock.py --resolution 1080x2340 --pattern "1,2,5,6,9" \\')
    print('     --scrcpy-path "/Users/<USER>/Downloads/scrcpy-macos-aarch64-v3.3.1/scrcpy"')
    print()
    print("For better accuracy (recommended for first test):")
    print('   python pattern_unlock.py --resolution 1080x2340 --pattern "1,2,5,6,9" \\')
    print('     --scrcpy-path "/Users/<USER>/Downloads/scrcpy-macos-aarch64-v3.3.1/scrcpy" \\')
    print('     --swipe-duration 1.5')
    print()
    print("Quick test (if screen is already awake):")
    print('   python pattern_unlock.py --resolution 1080x2340 --pattern "1,2,5,6,9" \\')
    print('     --scrcpy-path "/Users/<USER>/Downloads/scrcpy-macos-aarch64-v3.3.1/scrcpy" \\')
    print('     --no-wake')
    print()


def compare_devices():
    """Compare pattern positioning between devices."""
    
    print("Device Comparison Summary")
    print("=" * 25)
    print()
    
    devices = {
        "Galaxy Tab A 8\"": ("800x1280", "Tablet"),
        "Galaxy S21 FE": ("1080x2340", "Phone")
    }
    
    for device_name, (resolution, device_type) in devices.items():
        unlocker = PatternUnlocker(resolution)
        coords = unlocker.get_pattern_coordinates("1,2,5,6,9")
        
        print(f"{device_name}:")
        print(f"  Type: {device_type}")
        print(f"  Resolution: {resolution}")
        print(f"  Pattern coordinates: {coords}")
        
        # Calculate pattern area
        min_x = min(coord[0] for coord in coords)
        max_x = max(coord[0] for coord in coords)
        min_y = min(coord[1] for coord in coords)
        max_y = max(coord[1] for coord in coords)
        
        print(f"  Pattern area: {max_x - min_x}x{max_y - min_y} pixels")
        print(f"  Pattern center: ({(min_x + max_x) // 2}, {(min_y + max_y) // 2})")
        print()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "compare":
        # Run device comparison
        compare_devices()
    else:
        # Run S21 FE pattern test
        coordinates = test_s21_fe_pattern()
        show_s21_fe_usage()
        
        print("Troubleshooting Tips for S21 FE:")
        print("=" * 32)
        print("• S21 FE has a higher resolution, so coordinates are more precise")
        print("• Try --swipe-duration 1.5 for first test (slower = more accurate)")
        print("• Make sure USB debugging is NOT required (using --otg mode)")
        print("• If pattern doesn't align, the script may need fine-tuning for S21 FE lock screen")
        print("• S21 FE may have different lock screen layout than Tab A")
        print()
        print("Ready to test S21 FE unlock! 📱")
        print()
        print("To compare both devices, run:")
        print("python test_s21_fe.py compare")
