#!/usr/bin/env python3
"""
Samsung Pattern Unlock using macOS native touch simulation.
This version uses AppleScript and native macOS APIs for better compatibility with scrcpy.
"""

import argparse
import subprocess
import time
import sys
from typing import List, Tuple, Optional
from macos_touch_simulator import MacOSTouchSimulator

class MacOSPatternUnlocker:
    """Pattern unlocker using macOS native touch simulation."""
    
    def __init__(self, resolution: str, scrcpy_path: str = "scrcpy"):
        """
        Initialize the pattern unlocker.
        
        Args:
            resolution: Screen resolution in format "WIDTHxHEIGHT" (e.g., "1080x2340")
            scrcpy_path: Path to scrcpy executable
        """
        self.scrcpy_path = scrcpy_path
        self.width, self.height = self._parse_resolution(resolution)
        self.scrcpy_process: Optional[subprocess.Popen] = None
        self.touch_simulator = MacOSTouchSimulator()
        
        # Standard Android pattern grid positions (3x3 grid)
        self.pattern_grid = self._calculate_pattern_grid()
    
    def _parse_resolution(self, resolution: str) -> Tuple[int, int]:
        """Parse resolution string into width and height."""
        try:
            width, height = map(int, resolution.split('x'))
            return width, height
        except ValueError:
            raise ValueError(f"Invalid resolution format: {resolution}. Use WIDTHxHEIGHT format.")
    
    def _calculate_pattern_grid(self) -> List[List[Tuple[int, int]]]:
        """Calculate the 3x3 pattern grid coordinates based on screen resolution."""
        # Pattern grid is typically in the center-bottom area of the screen
        # These ratios work well for most Samsung devices
        
        grid_start_x_ratio = 0.2    # 20% from left edge
        grid_end_x_ratio = 0.8      # 80% from left edge (60% width)
        grid_start_y_ratio = 0.4    # 40% from top edge  
        grid_end_y_ratio = 0.75     # 75% from top edge (35% height)
        
        grid_start_x = int(self.width * grid_start_x_ratio)
        grid_end_x = int(self.width * grid_end_x_ratio)
        grid_start_y = int(self.height * grid_start_y_ratio)
        grid_end_y = int(self.height * grid_end_y_ratio)
        
        # Calculate 3x3 grid positions
        grid = []
        for row in range(3):
            grid_row = []
            for col in range(3):
                x = grid_start_x + (grid_end_x - grid_start_x) * col // 2
                y = grid_start_y + (grid_end_y - grid_start_y) * row // 2
                grid_row.append((x, y))
            grid.append(grid_row)
        
        return grid
    
    def get_pattern_coordinates(self, pattern: str) -> List[Tuple[int, int]]:
        """Convert pattern string to list of coordinates."""
        try:
            pattern_numbers = [int(x.strip()) for x in pattern.split(',')]
        except ValueError:
            raise ValueError(f"Invalid pattern format: {pattern}. Use comma-separated numbers 1-9.")
        
        coordinates = []
        for num in pattern_numbers:
            if num < 1 or num > 9:
                raise ValueError(f"Pattern number {num} out of range. Use numbers 1-9.")
            
            # Convert number to grid position (1-9 maps to 3x3 grid)
            row = (num - 1) // 3
            col = (num - 1) % 3
            coordinates.append(self.pattern_grid[row][col])
        
        return coordinates
    
    def start_scrcpy_otg(self) -> bool:
        """Start scrcpy in OTG mode."""
        try:
            print("Starting scrcpy in OTG mode...")
            cmd = [self.scrcpy_path, "--otg"]
            
            self.scrcpy_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give scrcpy time to start
            time.sleep(3)
            
            # Check if process is still running
            if self.scrcpy_process.poll() is not None:
                stdout, stderr = self.scrcpy_process.communicate()
                print("Failed to start scrcpy OTG mode")
                print(f"Error output: {stderr}")
                return False
            
            print("scrcpy OTG mode started successfully")
            print("You should see the device screen in a window")
            return True
            
        except Exception as e:
            print(f"Error starting scrcpy: {e}")
            return False
    
    def find_scrcpy_window_center(self) -> Tuple[int, int]:
        """
        Find the center of the scrcpy window.
        For now, we'll use a simple approach and ask user to position the window.
        """
        print("\nIMPORTANT: Please ensure the scrcpy window is visible and positioned where you can see it.")
        print("The pattern will be drawn relative to your screen center.")
        print("If the scrcpy window is not centered, the pattern may not align correctly.")
        
        # Use screen center as default
        # In a real implementation, you'd want to detect the actual window
        screen_width = 1440  # Typical MacBook screen width
        screen_height = 900  # Typical MacBook screen height
        
        return (screen_width // 2, screen_height // 2)
    
    def map_device_to_screen_coordinates(self, device_coords: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """
        Map device coordinates to screen coordinates for the scrcpy window.
        """
        window_center_x, window_center_y = self.find_scrcpy_window_center()
        
        # Assume scrcpy window is roughly 400x700 pixels (adjust as needed)
        window_width = 400
        window_height = 700
        
        window_left = window_center_x - window_width // 2
        window_top = window_center_y - window_height // 2
        
        screen_coords = []
        for device_x, device_y in device_coords:
            # Scale device coordinates to window coordinates
            scale_x = window_width / self.width
            scale_y = window_height / self.height
            
            screen_x = window_left + int(device_x * scale_x)
            screen_y = window_top + int(device_y * scale_y)
            
            screen_coords.append((screen_x, screen_y))
        
        return screen_coords
    
    def unlock_pattern(self, pattern: str, swipe_duration: float = 1.0) -> bool:
        """
        Perform the unlock pattern using macOS touch simulation.
        
        Args:
            pattern: Pattern string (e.g., "1,2,5,6,9")
            swipe_duration: Duration for the entire pattern drawing
        
        Returns:
            True if pattern was drawn successfully, False otherwise
        """
        try:
            print(f"Drawing unlock pattern: {pattern}")
            
            # Get device coordinates for the pattern
            device_coords = self.get_pattern_coordinates(pattern)
            print(f"Device coordinates: {device_coords}")
            
            # Map to screen coordinates
            screen_coords = self.map_device_to_screen_coordinates(device_coords)
            print(f"Screen coordinates: {screen_coords}")
            
            # Draw the pattern using macOS touch simulation
            success = self.touch_simulator.draw_pattern_line(
                screen_coords, 
                duration_per_segment=swipe_duration / len(screen_coords)
            )
            
            if success:
                print("✓ Pattern drawing completed successfully!")
                print("Check if the device unlocked.")
                return True
            else:
                print("✗ Pattern drawing failed!")
                return False
                
        except Exception as e:
            print(f"Error during pattern unlock: {e}")
            return False
    
    def cleanup(self) -> None:
        """Clean up resources and terminate scrcpy process."""
        if self.scrcpy_process and self.scrcpy_process.poll() is None:
            print("Terminating scrcpy process...")
            self.scrcpy_process.terminate()
            try:
                self.scrcpy_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("Force killing scrcpy process...")
                self.scrcpy_process.kill()
            self.scrcpy_process = None

def main():
    """Main function to run the pattern unlock."""
    parser = argparse.ArgumentParser(description="Unlock Samsung phone with pattern using macOS touch simulation")
    parser.add_argument("--resolution", required=True, help="Screen resolution (e.g., 1080x2340)")
    parser.add_argument("--pattern", required=True, help="Unlock pattern (e.g., 1,2,5,6,9)")
    parser.add_argument("--scrcpy-path", default="scrcpy", help="Path to scrcpy executable")
    parser.add_argument("--swipe-duration", type=float, default=2.0, help="Duration for pattern drawing")
    
    args = parser.parse_args()
    
    unlocker = MacOSPatternUnlocker(args.resolution, args.scrcpy_path)
    
    try:
        # Start scrcpy
        if not unlocker.start_scrcpy_otg():
            print("Failed to start scrcpy")
            return 1
        
        # Wait for user to position window
        input("\nPress Enter when the scrcpy window is visible and positioned correctly...")
        
        # Perform unlock
        success = unlocker.unlock_pattern(args.pattern, args.swipe_duration)
        
        if success:
            print("\n✓ Pattern unlock completed!")
        else:
            print("\n✗ Pattern unlock failed!")
            return 1
            
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1
    finally:
        unlocker.cleanup()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
