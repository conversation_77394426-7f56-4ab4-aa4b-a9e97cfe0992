#!/bin/bash
#
# Samsung Galaxy S21 FE Unlock Script
# 
# This script unlocks your Galaxy S21 FE using the pattern 1,2,5,6,9
# via scrcpy --otg mode (no ADB debugging required)
#
# Usage:
#   ./unlock_s21_fe.sh           # Normal unlock
#   ./unlock_s21_fe.sh slow      # Slower swipes for better accuracy
#   ./unlock_s21_fe.sh no-wake   # Don't wake screen first
#   ./unlock_s21_fe.sh test      # Test coordinates only
#

# Configuration for your Galaxy S21 FE
RESOLUTION="1080x2340"
PATTERN="1,2,5,6,9"
SCRCPY_PATH="/Users/<USER>/Downloads/scrcpy-macos-aarch64-v3.3.1/scrcpy"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if S21 FE is connected
check_connection() {
    print_status "Checking if Galaxy S21 FE is connected..."
    
    # Try to start scrcpy briefly to check connection
    timeout 5s "$SCRCPY_PATH" --otg --no-video --no-audio > /dev/null 2>&1
    local exit_code=$?
    
    if [ $exit_code -eq 0 ] || [ $exit_code -eq 124 ]; then
        print_success "Galaxy S21 FE detected and ready!"
        return 0
    else
        print_error "Galaxy S21 FE not detected. Please check:"
        echo "  • USB cable is connected"
        echo "  • Cable supports data transfer (not just charging)"
        echo "  • Phone is powered on"
        echo "  • Try different USB ports if needed"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Samsung Galaxy S21 FE Unlock Script"
    echo "==================================="
    echo ""
    echo "Usage:"
    echo "  $0                # Normal unlock (default)"
    echo "  $0 slow           # Slower swipes for better accuracy"
    echo "  $0 no-wake        # Don't wake screen first"
    echo "  $0 test           # Test coordinates only"
    echo "  $0 help           # Show this help"
    echo ""
    echo "Configuration:"
    echo "  Device: Samsung Galaxy S21 FE"
    echo "  Resolution: $RESOLUTION"
    echo "  Pattern: $PATTERN"
    echo "  scrcpy: $SCRCPY_PATH"
    echo ""
    echo "Pattern Layout:"
    echo "  1 2 ."
    echo "  . 3 4"
    echo "  . . 5"
    echo ""
}

# Function to test coordinates
test_coordinates() {
    print_status "Testing pattern coordinates for Galaxy S21 FE..."
    python3 "$SCRIPT_DIR/test_s21_fe.py"
}

# Function to unlock with different modes
unlock_s21_fe() {
    local mode="$1"
    local extra_args=""
    
    case "$mode" in
        "slow")
            extra_args="--swipe-duration 1.5"
            print_status "Using slow swipe mode for better accuracy..."
            print_warning "Recommended for first test on S21 FE"
            ;;
        "no-wake")
            extra_args="--no-wake"
            print_status "Skipping screen wake-up..."
            ;;
        *)
            print_status "Using normal unlock mode..."
            print_warning "If this fails, try: $0 slow"
            ;;
    esac
    
    print_status "Starting unlock sequence for Galaxy S21 FE..."
    print_status "Make sure your S21 FE is showing the lock screen"
    
    # Give user a moment to prepare
    echo "Starting in 3 seconds..."
    sleep 1
    echo "2..."
    sleep 1
    echo "1..."
    sleep 1
    
    # Run the unlock command
    python3 "$SCRIPT_DIR/pattern_unlock.py" \
        --resolution "$RESOLUTION" \
        --pattern "$PATTERN" \
        --scrcpy-path "$SCRCPY_PATH" \
        $extra_args
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        print_success "Unlock sequence completed! 🎉"
        print_status "Your Galaxy S21 FE should now be unlocked"
    else
        print_error "Unlock sequence failed!"
        echo ""
        echo "Troubleshooting tips for S21 FE:"
        echo "  • Try: $0 slow (for slower, more accurate swipes)"
        echo "  • S21 FE has higher resolution - coordinates are more precise"
        echo "  • Ensure phone is showing lock screen (not AOD or black screen)"
        echo "  • Check USB connection and try different ports"
        echo "  • Verify pattern is correct: $PATTERN"
        echo "  • S21 FE lock screen layout may differ slightly from Tab A"
    fi
    
    return $exit_code
}

# Main script logic
main() {
    local command="${1:-unlock}"
    
    echo "🔓 Samsung Galaxy S21 FE Pattern Unlock"
    echo "Resolution: $RESOLUTION | Pattern: $PATTERN"
    echo ""
    
    case "$command" in
        "help" | "-h" | "--help")
            show_usage
            exit 0
            ;;
        "test")
            test_coordinates
            exit 0
            ;;
        "slow" | "no-wake" | "unlock")
            # Check if required files exist
            if [ ! -f "$SCRIPT_DIR/pattern_unlock.py" ]; then
                print_error "pattern_unlock.py not found in $SCRIPT_DIR"
                exit 1
            fi
            
            if [ ! -f "$SCRCPY_PATH" ]; then
                print_error "scrcpy not found at: $SCRCPY_PATH"
                exit 1
            fi
            
            # Check connection
            if ! check_connection; then
                exit 1
            fi
            
            # Perform unlock
            unlock_s21_fe "$command"
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
