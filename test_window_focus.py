#!/usr/bin/env python3
"""
Test script to focus the scrcpy window and then send input.
This addresses the issue that input methods may need the window to have focus.
"""

import subprocess
import time
import sys
from typing import List, Tuple, Optional

class WindowFocusedInputTester:
    """Test input methods with proper window focusing."""
    
    def __init__(self, scrcpy_path: str = "scrcpy"):
        """Initialize the tester."""
        self.scrcpy_path = scrcpy_path
        self.scrcpy_process: Optional[subprocess.Popen] = None
        self.window_title = "SAMSUNG_Android"
        
        # Test coordinates - simple pattern: 1 -> 2 (horizontal line)
        self.test_coords = [
            (216, 936),   # Point 1 (top-left of pattern grid)
            (540, 936),   # Point 2 (top-center of pattern grid)
        ]
        
        print("Window-Focused Input Method Tester")
        print("=" * 40)
        print(f"Target window: {self.window_title}")
        print(f"Test pattern: Simple line from point 1 to point 2")
        print(f"Coordinates: {self.test_coords}")
        print()
    
    def start_scrcpy_otg(self) -> bool:
        """Start scrcpy in OTG mode."""
        try:
            print("🚀 Starting scrcpy in OTG mode...")
            cmd = [self.scrcpy_path, "--otg"]
            
            self.scrcpy_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give scrcpy time to start
            time.sleep(3)
            
            # Check if process is still running
            if self.scrcpy_process.poll() is not None:
                stdout, stderr = self.scrcpy_process.communicate()
                print("❌ Failed to start scrcpy OTG mode")
                print(f"Error output: {stderr}")
                return False
            
            print("✅ scrcpy OTG mode started successfully")
            print("📱 You should see a cursor appear on your phone screen")
            print()
            return True
            
        except Exception as e:
            print(f"❌ Error starting scrcpy: {e}")
            return False
    
    def focus_scrcpy_window(self) -> bool:
        """Focus the scrcpy window using AppleScript."""
        try:
            print(f"🎯 Attempting to focus window: {self.window_title}")
            
            # AppleScript to find and focus the scrcpy window
            applescript = f'''
            tell application "System Events"
                set targetApp to null
                set targetWindow to null
                
                -- Find the application with the scrcpy window
                repeat with proc in (every process whose background only is false)
                    try
                        repeat with win in (every window of proc)
                            if (name of win) contains "{self.window_title}" then
                                set targetApp to proc
                                set targetWindow to win
                                exit repeat
                            end if
                        end repeat
                        if targetApp is not null then exit repeat
                    end try
                end repeat
                
                -- Focus the window if found
                if targetApp is not null and targetWindow is not null then
                    set frontmost of targetApp to true
                    perform action "AXRaise" of targetWindow
                    return "SUCCESS: Window focused"
                else
                    return "ERROR: Window not found"
                end if
            end tell
            '''
            
            result = subprocess.run(['osascript', '-e', applescript], 
                                  capture_output=True, text=True, check=True)
            
            if "SUCCESS" in result.stdout:
                print("✅ Successfully focused scrcpy window")
                time.sleep(0.5)  # Give window time to focus
                return True
            else:
                print(f"❌ Failed to focus window: {result.stdout}")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"❌ AppleScript focus failed: {e}")
            return False
        except Exception as e:
            print(f"❌ Window focus error: {e}")
            return False
    
    def focus_with_cliclick(self) -> bool:
        """Alternative: Focus window by clicking on it."""
        try:
            print("🎯 Alternative: Focusing window with cliclick...")
            
            # Get window position using AppleScript
            applescript = f'''
            tell application "System Events"
                repeat with proc in (every process whose background only is false)
                    try
                        repeat with win in (every window of proc)
                            if (name of win) contains "{self.window_title}" then
                                set winPos to position of win
                                set winSize to size of win
                                return (item 1 of winPos) & "," & (item 2 of winPos) & "," & (item 1 of winSize) & "," & (item 2 of winSize)
                            end if
                        end repeat
                    end try
                end repeat
                return "NOT_FOUND"
            end tell
            '''
            
            result = subprocess.run(['osascript', '-e', applescript], 
                                  capture_output=True, text=True, check=True)
            
            if "NOT_FOUND" in result.stdout:
                print("❌ Window not found for clicking")
                return False
            
            # Parse window position and size
            x, y, w, h = map(int, result.stdout.strip().split(','))
            center_x = x + w // 2
            center_y = y + h // 2
            
            print(f"   Window found at: ({x}, {y}) size: ({w}, {h})")
            print(f"   Clicking center: ({center_x}, {center_y})")
            
            # Click on window center to focus it
            subprocess.run(['cliclick', f'c:{center_x},{center_y}'], check=True)
            time.sleep(0.5)
            
            print("✅ Clicked on window to focus")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ cliclick focus failed: {e}")
            return False
        except Exception as e:
            print(f"❌ Click focus error: {e}")
            return False
    
    def test_focused_cliclick_drag(self) -> bool:
        """Test cliclick drag with focused window."""
        print("\n🧪 TEST: Focused cliclick drag")
        
        try:
            # Focus window first
            if not self.focus_scrcpy_window():
                print("   Trying alternative focus method...")
                if not self.focus_with_cliclick():
                    print("   ❌ Could not focus window")
                    return False
            
            print("   ⏳ Starting focused drag test in 3 seconds...")
            for i in range(3, 0, -1):
                print(f"      {i}...")
                time.sleep(1)
            print("      GO!")
            
            start_x, start_y = self.test_coords[0]
            end_x, end_y = self.test_coords[1]
            
            print(f"   Moving to start: ({start_x}, {start_y})")
            subprocess.run(['cliclick', f'm:{start_x},{start_y}'], check=True)
            time.sleep(0.3)
            
            print(f"   Mouse down and drag to: ({end_x}, {end_y})")
            subprocess.run(['cliclick', 'dd:.'], check=True)
            time.sleep(0.2)
            subprocess.run(['cliclick', f'm:{end_x},{end_y}'], check=True)
            time.sleep(1.0)
            subprocess.run(['cliclick', 'du:.'], check=True)
            
            print("   ✅ Focused cliclick drag completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Focused cliclick drag failed: {e}")
            return False
        except Exception as e:
            print(f"   ❌ Focused drag error: {e}")
            return False
    
    def test_focused_pyautogui_drag(self) -> bool:
        """Test PyAutoGUI drag with focused window."""
        print("\n🧪 TEST: Focused PyAutoGUI drag")
        
        try:
            import pyautogui
            
            # Focus window first
            if not self.focus_scrcpy_window():
                print("   Trying alternative focus method...")
                if not self.focus_with_cliclick():
                    print("   ❌ Could not focus window")
                    return False
            
            print("   ⏳ Starting focused PyAutoGUI test in 3 seconds...")
            for i in range(3, 0, -1):
                print(f"      {i}...")
                time.sleep(1)
            print("      GO!")
            
            start_x, start_y = self.test_coords[0]
            end_x, end_y = self.test_coords[1]
            
            print(f"   PyAutoGUI drag from ({start_x}, {start_y}) to ({end_x}, {end_y})")
            pyautogui.moveTo(start_x, start_y)
            time.sleep(0.3)
            pyautogui.drag(end_x - start_x, end_y - start_y, duration=1.5, button='left')
            
            print("   ✅ Focused PyAutoGUI drag completed")
            return True
            
        except ImportError:
            print("   ❌ PyAutoGUI not available")
            return False
        except Exception as e:
            print(f"   ❌ Focused PyAutoGUI error: {e}")
            return False
    
    def test_window_relative_coordinates(self) -> bool:
        """Test using window-relative coordinates instead of screen coordinates."""
        print("\n🧪 TEST: Window-relative coordinates")
        
        try:
            # Get window position and size
            applescript = f'''
            tell application "System Events"
                repeat with proc in (every process whose background only is false)
                    try
                        repeat with win in (every window of proc)
                            if (name of win) contains "{self.window_title}" then
                                set winPos to position of win
                                set winSize to size of win
                                return (item 1 of winPos) & "," & (item 2 of winPos) & "," & (item 1 of winSize) & "," & (item 2 of winSize)
                            end if
                        end repeat
                    end try
                end repeat
                return "NOT_FOUND"
            end tell
            '''
            
            result = subprocess.run(['osascript', '-e', applescript], 
                                  capture_output=True, text=True, check=True)
            
            if "NOT_FOUND" in result.stdout:
                print("   ❌ Window not found")
                return False
            
            # Parse window position and size
            win_x, win_y, win_w, win_h = map(int, result.stdout.strip().split(','))
            print(f"   Window: pos=({win_x}, {win_y}) size=({win_w}, {win_h})")
            
            # Calculate relative coordinates within the window
            # Assume the pattern area is in the center portion of the window
            rel_start_x = win_w * 0.3  # 30% from left
            rel_start_y = win_h * 0.6  # 60% from top
            rel_end_x = win_w * 0.7    # 70% from left
            rel_end_y = win_h * 0.6    # Same Y (horizontal line)
            
            # Convert to screen coordinates
            screen_start_x = win_x + int(rel_start_x)
            screen_start_y = win_y + int(rel_start_y)
            screen_end_x = win_x + int(rel_end_x)
            screen_end_y = win_y + int(rel_end_y)
            
            print(f"   Relative coords: ({screen_start_x}, {screen_start_y}) to ({screen_end_x}, {screen_end_y})")
            
            # Focus window
            if not self.focus_scrcpy_window():
                if not self.focus_with_cliclick():
                    print("   ❌ Could not focus window")
                    return False
            
            print("   ⏳ Starting window-relative test in 3 seconds...")
            for i in range(3, 0, -1):
                print(f"      {i}...")
                time.sleep(1)
            print("      GO!")
            
            # Perform drag with window-relative coordinates
            subprocess.run(['cliclick', f'm:{screen_start_x},{screen_start_y}'], check=True)
            time.sleep(0.3)
            subprocess.run(['cliclick', 'dd:.'], check=True)
            time.sleep(0.2)
            subprocess.run(['cliclick', f'm:{screen_end_x},{screen_end_y}'], check=True)
            time.sleep(1.0)
            subprocess.run(['cliclick', 'du:.'], check=True)
            
            print("   ✅ Window-relative coordinates test completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Window-relative test failed: {e}")
            return False
        except Exception as e:
            print(f"   ❌ Window-relative error: {e}")
            return False
    
    def run_focused_tests(self) -> None:
        """Run all window-focused tests."""
        print("📋 WINDOW-FOCUSED TESTING:")
        print("   1. Make sure your phone shows the pattern lock screen")
        print("   2. Watch your phone screen during each test")
        print("   3. Each test will focus the scrcpy window first")
        print()
        
        print("🚀 Starting focused tests automatically in 5 seconds...")
        for i in range(5, 0, -1):
            print(f"   {i}...")
            time.sleep(1)
        print("   Starting tests now!")
        print()
        
        # List of test methods
        test_methods = [
            ("Focused cliclick drag", self.test_focused_cliclick_drag),
            ("Focused PyAutoGUI drag", self.test_focused_pyautogui_drag),
            ("Window-relative coordinates", self.test_window_relative_coordinates),
        ]
        
        results = {}
        
        for method_name, test_func in test_methods:
            try:
                success = test_func()
                results[method_name] = "✅ Completed" if success else "❌ Failed"
                
                # Pause between tests
                if method_name != test_methods[-1][0]:  # Not the last test
                    print(f"\n⏸️  Pausing 3 seconds before next test...")
                    time.sleep(3)
                    
            except KeyboardInterrupt:
                print(f"\n⏹️  Test interrupted during: {method_name}")
                break
            except Exception as e:
                results[method_name] = f"❌ Error: {e}"
        
        # Display results summary
        print("\n" + "=" * 50)
        print("📊 FOCUSED TEST RESULTS SUMMARY")
        print("=" * 50)
        
        for method_name, result in results.items():
            print(f"{method_name:25} : {result}")
        
        print("\n❓ QUESTIONS FOR YOU:")
        print("   - Did you see the cursor move on your phone during any test?")
        print("   - Did any test draw a line or show visual feedback?")
        print("   - Did focusing the window make a difference?")
        print()
    
    def cleanup(self) -> None:
        """Clean up resources."""
        if self.scrcpy_process and self.scrcpy_process.poll() is None:
            print("🧹 Terminating scrcpy process...")
            self.scrcpy_process.terminate()
            try:
                self.scrcpy_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("🔨 Force killing scrcpy process...")
                self.scrcpy_process.kill()
            self.scrcpy_process = None

def main():
    """Main function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test input methods with window focusing")
    parser.add_argument("--scrcpy-path", default="/Users/<USER>/Downloads/scrcpy-macos-aarch64-v3.3.1/scrcpy", 
                       help="Path to scrcpy executable")
    
    args = parser.parse_args()
    
    tester = WindowFocusedInputTester(args.scrcpy_path)
    
    try:
        # Start scrcpy
        if not tester.start_scrcpy_otg():
            print("❌ Failed to start scrcpy")
            return 1
        
        # Run focused tests
        tester.run_focused_tests()
        
        return 0
            
    except KeyboardInterrupt:
        print("\n⏹️  Testing interrupted by user")
        return 1
    except Exception as e:
        print(f"❌ Error: {e}")
        return 1
    finally:
        tester.cleanup()

if __name__ == "__main__":
    sys.exit(main())
