#!/usr/bin/env python3
"""
Pattern unlock using actual mouse input events for scrcpy --otg.
This simulates real mouse clicks and drags instead of just cursor movement.
"""

import argparse
import subprocess
import time
import sys
from typing import List, Tuple, Optional

class MouseInputUnlocker:
    """Pattern unlocker using actual mouse input events."""
    
    def __init__(self, resolution: str, scrcpy_path: str = "scrcpy"):
        """Initialize the pattern unlocker."""
        self.scrcpy_path = scrcpy_path
        self.width, self.height = self._parse_resolution(resolution)
        self.scrcpy_process: Optional[subprocess.Popen] = None
        
        # Check if cliclick is available
        self.cliclick_available = self._check_cliclick()
        
        # Standard Android pattern grid positions (3x3 grid)
        self.pattern_grid = self._calculate_pattern_grid()
    
    def _parse_resolution(self, resolution: str) -> Tuple[int, int]:
        """Parse resolution string into width and height."""
        try:
            width, height = map(int, resolution.split('x'))
            return width, height
        except ValueError:
            raise ValueError(f"Invalid resolution format: {resolution}. Use WIDTHxHEIGHT format.")
    
    def _check_cliclick(self) -> bool:
        """Check if cliclick is available."""
        try:
            result = subprocess.run(['which', 'cliclick'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✓ cliclick found")
                return True
            else:
                print("✗ cliclick not found")
                return False
        except Exception:
            print("✗ cliclick not found")
            return False
    
    def _calculate_pattern_grid(self) -> List[List[Tuple[int, int]]]:
        """Calculate the 3x3 pattern grid coordinates."""
        # Pattern grid ratios for Samsung devices
        grid_start_x_ratio = 0.2    # 20% from left edge
        grid_end_x_ratio = 0.8      # 80% from left edge
        grid_start_y_ratio = 0.4    # 40% from top edge  
        grid_end_y_ratio = 0.75     # 75% from top edge
        
        grid_start_x = int(self.width * grid_start_x_ratio)
        grid_end_x = int(self.width * grid_end_x_ratio)
        grid_start_y = int(self.height * grid_start_y_ratio)
        grid_end_y = int(self.height * grid_end_y_ratio)
        
        # Calculate 3x3 grid positions
        grid = []
        for row in range(3):
            grid_row = []
            for col in range(3):
                x = grid_start_x + (grid_end_x - grid_start_x) * col // 2
                y = grid_start_y + (grid_end_y - grid_start_y) * row // 2
                grid_row.append((x, y))
            grid.append(grid_row)
        
        return grid
    
    def get_pattern_coordinates(self, pattern: str) -> List[Tuple[int, int]]:
        """Convert pattern string to list of device coordinates."""
        try:
            pattern_numbers = [int(x.strip()) for x in pattern.split(',')]
        except ValueError:
            raise ValueError(f"Invalid pattern format: {pattern}. Use comma-separated numbers 1-9.")
        
        coordinates = []
        for num in pattern_numbers:
            if num < 1 or num > 9:
                raise ValueError(f"Pattern number {num} out of range. Use numbers 1-9.")
            
            # Convert number to grid position (1-9 maps to 3x3 grid)
            row = (num - 1) // 3
            col = (num - 1) % 3
            coordinates.append(self.pattern_grid[row][col])
        
        return coordinates
    
    def start_scrcpy_otg(self) -> bool:
        """Start scrcpy in OTG mode."""
        try:
            print("Starting scrcpy in OTG mode...")
            cmd = [self.scrcpy_path, "--otg"]
            
            self.scrcpy_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give scrcpy time to start
            time.sleep(3)
            
            # Check if process is still running
            if self.scrcpy_process.poll() is not None:
                stdout, stderr = self.scrcpy_process.communicate()
                print("Failed to start scrcpy OTG mode")
                print(f"Error output: {stderr}")
                return False
            
            print("✓ scrcpy OTG mode started successfully")
            print("You should see a cursor appear on your phone screen")
            return True
            
        except Exception as e:
            print(f"Error starting scrcpy: {e}")
            return False
    
    def send_mouse_click(self, x: int, y: int) -> bool:
        """Send a single mouse click at coordinates."""
        try:
            subprocess.run(['cliclick', f'c:{x},{y}'], check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"Mouse click failed: {e}")
            return False
    
    def send_mouse_drag(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 0.5) -> bool:
        """Send a mouse drag from start to end coordinates."""
        try:
            # Move to start position
            subprocess.run(['cliclick', f'm:{start_x},{start_y}'], check=True)
            time.sleep(0.1)
            
            # Press and hold mouse button
            subprocess.run(['cliclick', 'dd:.'], check=True)
            time.sleep(0.1)
            
            # Drag to end position
            subprocess.run(['cliclick', f'm:{end_x},{end_y}'], check=True)
            time.sleep(duration)
            
            # Release mouse button
            subprocess.run(['cliclick', 'du:.'], check=True)
            
            return True
        except subprocess.CalledProcessError as e:
            print(f"Mouse drag failed: {e}")
            return False
    
    def draw_pattern_with_mouse_events(self, coordinates: List[Tuple[int, int]], duration: float = 2.0) -> bool:
        """Draw pattern using proper mouse click and drag events."""
        if not self.cliclick_available:
            print("cliclick not available")
            return False
        
        if len(coordinates) < 2:
            print("Need at least 2 points to draw a pattern")
            return False
        
        try:
            print(f"Drawing pattern through {len(coordinates)} points using mouse events...")
            
            # Method 1: Single continuous drag
            start_x, start_y = coordinates[0]
            print(f"Starting drag at: ({start_x}, {start_y})")
            
            # Move to start position
            subprocess.run(['cliclick', f'm:{start_x},{start_y}'], check=True)
            time.sleep(0.3)
            
            # Press down to start drag
            subprocess.run(['cliclick', 'dd:.'], check=True)
            print("Mouse down - started dragging...")
            time.sleep(0.2)
            
            # Drag through each point
            segment_duration = duration / (len(coordinates) - 1)
            
            for i in range(1, len(coordinates)):
                end_x, end_y = coordinates[i]
                print(f"Dragging to point {i+1}: ({end_x}, {end_y})")
                
                # Drag to next point
                subprocess.run(['cliclick', f'm:{end_x},{end_y}'], check=True)
                time.sleep(segment_duration)
            
            # Release mouse to end drag
            subprocess.run(['cliclick', 'du:.'], check=True)
            print("Mouse up - finished dragging")
            
            print("✓ Pattern drawing completed!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"Mouse event failed: {e}")
            return False
        except Exception as e:
            print(f"Error drawing pattern: {e}")
            return False
    
    def draw_pattern_with_clicks(self, coordinates: List[Tuple[int, int]]) -> bool:
        """Alternative: Draw pattern using individual clicks at each point."""
        if not self.cliclick_available:
            print("cliclick not available")
            return False
        
        try:
            print(f"Drawing pattern using clicks at {len(coordinates)} points...")
            
            for i, (x, y) in enumerate(coordinates):
                print(f"Clicking point {i+1}: ({x}, {y})")
                subprocess.run(['cliclick', f'c:{x},{y}'], check=True)
                time.sleep(0.5)
            
            print("✓ Pattern clicking completed!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"Click pattern failed: {e}")
            return False
        except Exception as e:
            print(f"Error clicking pattern: {e}")
            return False
    
    def unlock_pattern(self, pattern: str, method: str = "drag", swipe_duration: float = 2.0) -> bool:
        """Perform the unlock pattern using specified method."""
        try:
            print(f"\nPreparing to draw unlock pattern: {pattern}")
            print(f"Method: {method}")
            
            # Get device coordinates for the pattern
            device_coords = self.get_pattern_coordinates(pattern)
            print(f"Pattern coordinates: {device_coords}")
            
            # Wait with countdown
            print("\nIMPORTANT:")
            print("- Make sure your phone screen is showing the pattern lock")
            print("- The cursor should be visible on your phone screen")
            print("- Don't touch your phone during pattern drawing")
            
            print("\nStarting pattern unlock in:")
            for i in range(3, 0, -1):
                print(f"{i}...")
                time.sleep(1)
            print("Drawing pattern now!")
            
            # Draw the pattern using specified method
            if method == "drag":
                success = self.draw_pattern_with_mouse_events(device_coords, swipe_duration)
            elif method == "clicks":
                success = self.draw_pattern_with_clicks(device_coords)
            else:
                print(f"Unknown method: {method}")
                return False
            
            if success:
                print("\n✓ Pattern unlock completed!")
                print("Check if your phone unlocked.")
                return True
            else:
                print("\n✗ Pattern unlock failed!")
                return False
                
        except Exception as e:
            print(f"Error during pattern unlock: {e}")
            return False
    
    def cleanup(self) -> None:
        """Clean up resources."""
        if self.scrcpy_process and self.scrcpy_process.poll() is None:
            print("Terminating scrcpy process...")
            self.scrcpy_process.terminate()
            try:
                self.scrcpy_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("Force killing scrcpy process...")
                self.scrcpy_process.kill()
            self.scrcpy_process = None

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Samsung pattern unlock using mouse input events")
    parser.add_argument("--resolution", required=True, help="Screen resolution (e.g., 1080x2340)")
    parser.add_argument("--pattern", required=True, help="Unlock pattern (e.g., 1,2,5,6,9)")
    parser.add_argument("--method", choices=["drag", "clicks"], default="drag", help="Input method: drag or clicks")
    parser.add_argument("--scrcpy-path", default="scrcpy", help="Path to scrcpy executable")
    parser.add_argument("--swipe-duration", type=float, default=2.0, help="Duration for pattern drawing")
    
    args = parser.parse_args()
    
    unlocker = MouseInputUnlocker(args.resolution, args.scrcpy_path)
    
    try:
        # Start scrcpy
        if not unlocker.start_scrcpy_otg():
            print("Failed to start scrcpy")
            return 1
        
        # Perform unlock
        success = unlocker.unlock_pattern(args.pattern, args.method, args.swipe_duration)
        
        return 0 if success else 1
            
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1
    finally:
        unlocker.cleanup()

if __name__ == "__main__":
    sys.exit(main())
