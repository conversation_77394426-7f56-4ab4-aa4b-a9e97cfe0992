#!/usr/bin/env python3
"""
Samsung Phone Pattern Unlock Script using scrcpy --otg

This script sends swipe commands to unlock a Samsung phone with a known pattern.
It uses scrcpy in OTG mode to send touch events without requiring ADB debugging.

Requirements:
- scrcpy installed and accessible in PATH
- Samsung phone connected via USB
- Known screen resolution
- Known unlock pattern coordinates

Usage:
    python pattern_unlock.py --resolution 1080x2340 --pattern "1,2,3,6,9"
"""

import subprocess
import time
import argparse
import sys
from typing import List, Tuple, Optional
import math


class PatternUnlocker:
    def __init__(self, resolution: str, scrcpy_path: str = "scrcpy"):
        """
        Initialize the pattern unlocker.
        
        Args:
            resolution: Screen resolution in format "WIDTHxHEIGHT" (e.g., "1080x2340")
            scrcpy_path: Path to scrcpy executable
        """
        self.scrcpy_path = scrcpy_path
        self.width, self.height = self._parse_resolution(resolution)
        self.scrcpy_process: Optional[subprocess.Popen] = None
        
        # Standard Android pattern grid positions (3x3 grid)
        # These are relative positions that will be scaled to actual screen size
        self.pattern_grid = self._calculate_pattern_grid()
    
    def _parse_resolution(self, resolution: str) -> Tuple[int, int]:
        """Parse resolution string into width and height."""
        try:
            width, height = map(int, resolution.split('x'))
            return width, height
        except ValueError:
            raise ValueError(f"Invalid resolution format: {resolution}. Use WIDTHxHEIGHT format.")
    
    def _calculate_pattern_grid(self) -> List[List[Tuple[int, int]]]:
        """
        Calculate the 3x3 pattern grid coordinates based on screen resolution.
        
        Returns a 3x3 grid where each cell contains (x, y) coordinates.
        Grid layout:
        1 2 3
        4 5 6
        7 8 9
        """
        # Pattern area is typically in the center-bottom portion of the screen
        # Adjust these ratios based on your Samsung phone's lock screen layout
        pattern_start_y_ratio = 0.4  # Pattern starts at 40% from top
        pattern_height_ratio = 0.35   # Pattern takes 35% of screen height
        pattern_width_ratio = 0.6     # Pattern takes 60% of screen width
        
        # Calculate pattern area bounds
        pattern_width = int(self.width * pattern_width_ratio)
        pattern_height = int(self.height * pattern_height_ratio)
        pattern_start_x = (self.width - pattern_width) // 2
        pattern_start_y = int(self.height * pattern_start_y_ratio)
        
        # Calculate grid positions
        grid = []
        for row in range(3):
            grid_row = []
            for col in range(3):
                x = pattern_start_x + (pattern_width * col // 2)
                y = pattern_start_y + (pattern_height * row // 2)
                grid_row.append((x, y))
            grid.append(grid_row)
        
        return grid
    
    def get_pattern_coordinates(self, pattern: str) -> List[Tuple[int, int]]:
        """
        Convert pattern string to list of coordinates.
        
        Args:
            pattern: Comma-separated string of pattern numbers (1-9)
                    e.g., "1,2,3,6,9" for L-shape pattern
        
        Returns:
            List of (x, y) coordinates for the pattern
        """
        try:
            pattern_numbers = [int(x.strip()) for x in pattern.split(',')]
        except ValueError:
            raise ValueError(f"Invalid pattern format: {pattern}. Use comma-separated numbers 1-9.")
        
        coordinates = []
        for num in pattern_numbers:
            if not (1 <= num <= 9):
                raise ValueError(f"Pattern number {num} is invalid. Use numbers 1-9.")
            
            # Convert number to grid position (1-9 maps to 3x3 grid)
            row = (num - 1) // 3
            col = (num - 1) % 3
            coordinates.append(self.pattern_grid[row][col])
        
        return coordinates
    
    def start_scrcpy_otg(self) -> bool:
        """
        Start scrcpy in OTG mode.
        
        Returns:
            True if scrcpy started successfully, False otherwise
        """
        try:
            cmd = [self.scrcpy_path, "--otg"]
            self.scrcpy_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give scrcpy time to initialize
            time.sleep(2)
            
            # Check if process is still running
            if self.scrcpy_process.poll() is None:
                print("scrcpy OTG mode started successfully")
                return True
            else:
                print("Failed to start scrcpy OTG mode")
                return False
                
        except FileNotFoundError:
            print(f"scrcpy not found at: {self.scrcpy_path}")
            print("Please install scrcpy or provide correct path")
            return False
        except Exception as e:
            print(f"Error starting scrcpy: {e}")
            return False
    
    def send_touch_event(self, x: int, y: int, action: str = "down") -> bool:
        """
        Send a touch event to the device.
        
        Args:
            x, y: Touch coordinates
            action: Touch action ("down", "up", "move")
        
        Returns:
            True if command sent successfully, False otherwise
        """
        if not self.scrcpy_process or self.scrcpy_process.poll() is not None:
            print("scrcpy process not running")
            return False
        
        try:
            # scrcpy OTG touch event format: "touch x y action"
            command = f"touch {x} {y} {action}\n"
            self.scrcpy_process.stdin.write(command)
            self.scrcpy_process.stdin.flush()
            return True
        except Exception as e:
            print(f"Error sending touch event: {e}")
            return False
    
    def draw_pattern(self, pattern: str, swipe_duration: float = 0.5) -> bool:
        """
        Draw the unlock pattern on the screen.
        
        Args:
            pattern: Pattern string (e.g., "1,2,3,6,9")
            swipe_duration: Duration for each swipe segment in seconds
        
        Returns:
            True if pattern drawn successfully, False otherwise
        """
        coordinates = self.get_pattern_coordinates(pattern)
        
        if len(coordinates) < 2:
            print("Pattern must have at least 2 points")
            return False
        
        print(f"Drawing pattern: {pattern}")
        print(f"Coordinates: {coordinates}")
        
        # Start the pattern by touching down at first point
        start_x, start_y = coordinates[0]
        if not self.send_touch_event(start_x, start_y, "down"):
            return False
        
        # Draw lines between consecutive points
        for i in range(1, len(coordinates)):
            end_x, end_y = coordinates[i]
            
            # Calculate intermediate points for smooth swipe
            start_x, start_y = coordinates[i-1]
            steps = max(10, int(swipe_duration * 60))  # 60 FPS approximation
            
            for step in range(1, steps + 1):
                progress = step / steps
                current_x = int(start_x + (end_x - start_x) * progress)
                current_y = int(start_y + (end_y - start_y) * progress)
                
                if not self.send_touch_event(current_x, current_y, "move"):
                    return False
                
                time.sleep(swipe_duration / steps)
        
        # Release touch at the end
        final_x, final_y = coordinates[-1]
        if not self.send_touch_event(final_x, final_y, "up"):
            return False
        
        print("Pattern drawing completed")
        return True
    
    def wake_screen(self) -> bool:
        """
        Wake up the screen by sending a power button press.
        
        Returns:
            True if command sent successfully, False otherwise
        """
        try:
            if self.scrcpy_process and self.scrcpy_process.poll() is None:
                command = "key POWER\n"
                self.scrcpy_process.stdin.write(command)
                self.scrcpy_process.stdin.flush()
                time.sleep(1)  # Wait for screen to wake up
                return True
        except Exception as e:
            print(f"Error waking screen: {e}")
        return False
    
    def unlock_phone(self, pattern: str, wake_first: bool = True) -> bool:
        """
        Complete unlock sequence: wake screen and draw pattern.
        
        Args:
            pattern: Pattern string (e.g., "1,2,3,6,9")
            wake_first: Whether to wake the screen first
        
        Returns:
            True if unlock sequence completed successfully, False otherwise
        """
        if wake_first:
            print("Waking up screen...")
            self.wake_screen()
            time.sleep(1)
        
        print("Drawing unlock pattern...")
        return self.draw_pattern(pattern)
    
    def stop_scrcpy(self):
        """Stop the scrcpy process."""
        if self.scrcpy_process:
            self.scrcpy_process.terminate()
            self.scrcpy_process.wait()
            print("scrcpy process stopped")


def main():
    parser = argparse.ArgumentParser(description="Unlock Samsung phone using pattern via scrcpy OTG")
    parser.add_argument("--resolution", required=True, 
                       help="Screen resolution in WIDTHxHEIGHT format (e.g., 1080x2340)")
    parser.add_argument("--pattern", required=True,
                       help="Unlock pattern as comma-separated numbers 1-9 (e.g., '1,2,3,6,9')")
    parser.add_argument("--scrcpy-path", default="scrcpy",
                       help="Path to scrcpy executable (default: scrcpy)")
    parser.add_argument("--no-wake", action="store_true",
                       help="Don't wake screen before drawing pattern")
    parser.add_argument("--swipe-duration", type=float, default=0.5,
                       help="Duration for each swipe segment in seconds (default: 0.5)")
    
    args = parser.parse_args()
    
    try:
        unlocker = PatternUnlocker(args.resolution, args.scrcpy_path)
        
        if not unlocker.start_scrcpy_otg():
            sys.exit(1)
        
        success = unlocker.unlock_phone(args.pattern, wake_first=not args.no_wake)
        
        if success:
            print("Unlock sequence completed successfully!")
        else:
            print("Unlock sequence failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nInterrupted by user")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
    finally:
        if 'unlocker' in locals():
            unlocker.stop_scrcpy()


if __name__ == "__main__":
    main()
