# Samsung Phone Pattern Unlock Script

A Python script that sends swipe commands to unlock a Samsung phone with a known pattern using scrcpy in OTG mode. This is useful when ADB debugging is not enabled on the device.

## Features

- Unlock Samsung phones using pattern gestures
- Works with scrcpy --otg mode (no ADB debugging required)
- Configurable screen resolution support
- Automatic screen wake-up
- Smooth pattern drawing with configurable swipe duration
- Support for any 3x3 pattern grid unlock pattern

## Prerequisites

1. **scrcpy** must be installed and accessible in your PATH
   - Download from: https://github.com/Genymobile/scrcpy
   - Follow installation instructions for your operating system

2. **USB connection** between your computer and Samsung phone
   - USB debugging is NOT required (uses OTG mode)

3. **Known screen resolution** of your Samsung phone

4. **Known unlock pattern** (sequence of numbers 1-9 representing the 3x3 grid)

## Pattern Grid Layout

The unlock pattern uses a 3x3 grid numbered as follows:

```
1 2 3
4 5 6
7 8 9
```

For example:
- L-shape pattern: `1,2,3,6,9`
- Z-shape pattern: `1,2,3,4,7,8,9`
- Simple line: `1,5,9`

## Usage

### Basic Usage

```bash
python pattern_unlock.py --resolution 1080x2340 --pattern "1,2,3,6,9"
```

### Advanced Usage

```bash
# Custom scrcpy path
python pattern_unlock.py --resolution 1080x2340 --pattern "1,2,3,6,9" --scrcpy-path "/path/to/scrcpy"

# Don't wake screen (if already awake)
python pattern_unlock.py --resolution 1080x2340 --pattern "1,2,3,6,9" --no-wake

# Slower swipe duration for better accuracy
python pattern_unlock.py --resolution 1080x2340 --pattern "1,2,3,6,9" --swipe-duration 1.0
```

### Command Line Arguments

- `--resolution`: Screen resolution in WIDTHxHEIGHT format (required)
- `--pattern`: Unlock pattern as comma-separated numbers 1-9 (required)
- `--scrcpy-path`: Path to scrcpy executable (default: "scrcpy")
- `--no-wake`: Don't wake screen before drawing pattern
- `--swipe-duration`: Duration for each swipe segment in seconds (default: 0.5)

## Finding Your Screen Resolution

### Method 1: Device Settings
1. Go to Settings → Display → Screen resolution
2. Note the resolution (e.g., 1080 x 2340)

### Method 2: Using scrcpy
1. Connect your phone and run: `scrcpy --print-fps`
2. The resolution will be displayed in the window title

### Method 3: Online lookup
Search for your Samsung phone model specifications online.

## Common Samsung Phone Resolutions

- Galaxy S21/S22: `1080x2400`
- Galaxy S20: `1440x3200`
- Galaxy Note 20: `1080x2400`
- Galaxy A series: `1080x2340` or `720x1600`

## Troubleshooting

### scrcpy not found
- Ensure scrcpy is installed and in your PATH
- Use `--scrcpy-path` to specify full path to scrcpy executable

### Pattern not working
- Verify your screen resolution is correct
- Try adjusting `--swipe-duration` (slower = more accurate)
- Check that your pattern sequence is correct
- Ensure the phone screen is on and showing the lock screen

### Connection issues
- Make sure USB cable is properly connected
- Try different USB ports
- Some phones may require specific USB connection modes

### Pattern grid positioning
If the default pattern grid doesn't align with your lock screen:
1. Edit the `_calculate_pattern_grid()` method in the script
2. Adjust the ratio values:
   - `pattern_start_y_ratio`: Where pattern area starts vertically
   - `pattern_height_ratio`: Height of pattern area
   - `pattern_width_ratio`: Width of pattern area

## Security Note

This script is intended for unlocking your own device when you know the pattern but cannot input it manually (e.g., broken screen, accessibility needs). Do not use this script on devices you do not own or without proper authorization.

## License

This script is provided as-is for educational and personal use purposes.
