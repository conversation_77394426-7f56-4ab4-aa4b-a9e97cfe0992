#!/usr/bin/env python3
"""
Test cursor movement without scrcpy to verify cliclick is working.
This will draw a simple pattern on your screen to test the automation.
"""

import subprocess
import time
import sys

def test_cliclick_basic():
    """Test basic cliclick functionality."""
    print("Testing basic cliclick functionality...")
    
    try:
        # Get current cursor position
        result = subprocess.run(['cliclick', 'p'], capture_output=True, text=True, check=True)
        current_pos = result.stdout.strip()
        print(f"Current cursor position: {current_pos}")
        
        # Parse current position
        x, y = map(int, current_pos.split(','))
        print(f"Parsed position: x={x}, y={y}")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"cliclick basic test failed: {e}")
        return False
    except Exception as e:
        print(f"Error in basic test: {e}")
        return False

def test_cursor_movement():
    """Test cursor movement by drawing a simple square."""
    print("\nTesting cursor movement...")
    print("This will draw a small square with your cursor in 3 seconds...")
    
    # Countdown
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    try:
        # Get current position as starting point
        result = subprocess.run(['cliclick', 'p'], capture_output=True, text=True, check=True)
        start_x, start_y = map(int, result.stdout.strip().split(','))
        
        print(f"Starting square at: ({start_x}, {start_y})")
        
        # Define a small square (50x50 pixels)
        size = 50
        square_points = [
            (start_x, start_y),                    # Top-left (start)
            (start_x + size, start_y),             # Top-right
            (start_x + size, start_y + size),      # Bottom-right
            (start_x, start_y + size),             # Bottom-left
            (start_x, start_y),                    # Back to start
        ]
        
        print("Drawing square...")
        
        # Move to first point
        subprocess.run(['cliclick', f'm:{square_points[0][0]},{square_points[0][1]}'], check=True)
        time.sleep(0.5)
        
        # Start drawing (mouse down)
        subprocess.run(['cliclick', 'dd:.'], check=True)
        print("Started drawing...")
        
        # Draw to each point
        for i in range(1, len(square_points)):
            x, y = square_points[i]
            print(f"Drawing to: ({x}, {y})")
            subprocess.run(['cliclick', f'm:{x},{y}'], check=True)
            time.sleep(0.5)
        
        # End drawing (mouse up)
        subprocess.run(['cliclick', 'du:.'], check=True)
        print("✓ Square drawing completed!")
        
        # Return cursor to original position
        subprocess.run(['cliclick', f'm:{start_x},{start_y}'], check=True)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"cliclick movement test failed: {e}")
        return False
    except Exception as e:
        print(f"Error in movement test: {e}")
        return False

def test_pattern_coordinates():
    """Test the same coordinates we use for the phone pattern."""
    print("\nTesting phone pattern coordinates...")
    print("This will draw the unlock pattern on your screen in 3 seconds...")
    
    # Use the same coordinates as the phone pattern
    pattern_coords = [(216, 936), (540, 936), (540, 1345), (864, 1345), (864, 1755)]
    
    # Scale them down to fit on screen (divide by 4)
    screen_coords = [(x//4, y//4) for x, y in pattern_coords]
    print(f"Scaled coordinates: {screen_coords}")
    
    # Countdown
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    try:
        print("Drawing pattern...")
        
        # Move to first point
        start_x, start_y = screen_coords[0]
        subprocess.run(['cliclick', f'm:{start_x},{start_y}'], check=True)
        time.sleep(0.5)
        
        # Start drawing
        subprocess.run(['cliclick', 'dd:.'], check=True)
        print(f"Started at: ({start_x}, {start_y})")
        
        # Draw to each subsequent point
        for i in range(1, len(screen_coords)):
            x, y = screen_coords[i]
            print(f"Drawing to point {i+1}: ({x}, {y})")
            subprocess.run(['cliclick', f'm:{x},{y}'], check=True)
            time.sleep(0.8)
        
        # End drawing
        subprocess.run(['cliclick', 'du:.'], check=True)
        print("✓ Pattern drawing completed!")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"Pattern test failed: {e}")
        return False
    except Exception as e:
        print(f"Error in pattern test: {e}")
        return False

def main():
    """Main test function."""
    print("Cursor Movement Test (No Phone Required)")
    print("=" * 45)
    print("This will test if cliclick can move your cursor properly.")
    print("Make sure you can see your screen and cursor.")
    print()
    
    # Test 1: Basic functionality
    if not test_cliclick_basic():
        print("❌ Basic cliclick test failed!")
        return 1
    
    print("✅ Basic cliclick test passed!")
    
    # Test 2: Simple movement (square)
    response = input("\nDraw a test square? (y/N): ")
    if response.lower() == 'y':
        if test_cursor_movement():
            print("✅ Cursor movement test passed!")
        else:
            print("❌ Cursor movement test failed!")
            return 1
    
    # Test 3: Pattern coordinates
    response = input("\nDraw the unlock pattern on screen? (y/N): ")
    if response.lower() == 'y':
        if test_pattern_coordinates():
            print("✅ Pattern coordinate test passed!")
        else:
            print("❌ Pattern coordinate test failed!")
            return 1
    
    print("\n" + "=" * 45)
    print("All tests completed!")
    print("If you saw the cursor moving, cliclick is working correctly.")
    print("If the cursor didn't move, there may be an issue with:")
    print("1. macOS accessibility permissions")
    print("2. cliclick installation")
    print("3. System security settings")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
