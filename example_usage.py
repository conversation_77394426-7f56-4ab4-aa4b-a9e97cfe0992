#!/usr/bin/env python3
"""
Example usage of the PatternUnlocker class.

This demonstrates how to use the PatternUnlocker programmatically
rather than through the command line interface.
"""

from pattern_unlock import PatternUnlocker
import time


def example_unlock():
    """Example of unlocking a Samsung phone with a known pattern."""
    
    # Configuration - adjust these for your device
    SCREEN_RESOLUTION = "800x1280"   # Samsung Galaxy Tab A 8-inch resolution
    UNLOCK_PATTERN = "1,2,5,6,9"     # User's unlock pattern
    
    print("Samsung Phone Pattern Unlock Example")
    print("=" * 40)
    print(f"Screen Resolution: {SCREEN_RESOLUTION}")
    print(f"Unlock Pattern: {UNLOCK_PATTERN}")
    print()
    
    try:
        # Create the unlocker instance
        unlocker = PatternUnlocker(SCREEN_RESOLUTION)
        
        # Start scrcpy in OTG mode
        print("Starting scrcpy in OTG mode...")
        if not unlocker.start_scrcpy_otg():
            print("Failed to start scrcpy. Please check your installation.")
            return False
        
        print("scrcpy started successfully!")
        print("Make sure your Samsung phone is connected via USB.")
        print()
        
        # Wait a moment for user to prepare
        print("Starting unlock sequence in 3 seconds...")
        for i in range(3, 0, -1):
            print(f"{i}...")
            time.sleep(1)
        
        # Perform the unlock
        print("Unlocking phone...")
        success = unlocker.unlock_phone(UNLOCK_PATTERN)
        
        if success:
            print("✓ Unlock sequence completed successfully!")
        else:
            print("✗ Unlock sequence failed!")
            
        return success
        
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        # Always clean up
        if 'unlocker' in locals():
            unlocker.stop_scrcpy()


def test_pattern_coordinates():
    """Test function to display pattern coordinates for verification."""
    
    SCREEN_RESOLUTION = "800x1280"  # Samsung Galaxy Tab A 8-inch
    TEST_PATTERNS = [
        "1,2,5,6,9",      # User's pattern
        "1,2,3,6,9",      # L-shape
        "1,5,9",          # Diagonal
        "2,4,6,8",        # Cross
        "1,2,3,4,7,8,9",  # Z-shape
    ]
    
    print("Pattern Coordinate Test")
    print("=" * 30)
    
    unlocker = PatternUnlocker(SCREEN_RESOLUTION)
    
    for pattern in TEST_PATTERNS:
        print(f"\nPattern: {pattern}")
        try:
            coordinates = unlocker.get_pattern_coordinates(pattern)
            print(f"Coordinates: {coordinates}")
            
            # Display as grid for visualization
            print("Grid visualization:")
            grid_display = [["." for _ in range(3)] for _ in range(3)]
            
            for i, num in enumerate(map(int, pattern.split(','))):
                row = (num - 1) // 3
                col = (num - 1) % 3
                grid_display[row][col] = str(i + 1)
            
            for row in grid_display:
                print("  " + " ".join(row))
                
        except ValueError as e:
            print(f"Error: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # Run coordinate test
        test_pattern_coordinates()
    else:
        # Run actual unlock example
        print("IMPORTANT: Make sure you have:")
        print("1. scrcpy installed and in PATH")
        print("2. Samsung phone connected via USB")
        print("3. Correct screen resolution and pattern configured")
        print()
        
        response = input("Continue with unlock attempt? (y/N): ")
        if response.lower() == 'y':
            example_unlock()
        else:
            print("Operation cancelled.")
            print("\nTo test pattern coordinates without unlocking, run:")
            print("python example_usage.py test")
