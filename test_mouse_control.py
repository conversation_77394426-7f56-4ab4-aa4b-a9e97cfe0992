#!/usr/bin/env python3
"""
Test script for mouse-controlled pattern unlock using scrcpy --otg mode.
This script tests the new approach using PyAutoGUI to control the mouse cursor.
"""

from pattern_unlock import Pat<PERSON>Unlock<PERSON>

def test_mouse_control():
    """Test the mouse control approach for pattern unlock."""
    
    print("=== Testing Mouse-Controlled Pattern Unlock ===")
    print()
    
    # Test with Galaxy S21 FE settings
    scrcpy_path = "/Users/<USER>/Downloads/scrcpy-macos-aarch64-v3.3.1/scrcpy"
    resolution = "1080x2340"

    unlocker = PatternUnlocker(resolution, scrcpy_path)
    
    print("1. Starting scrcpy in OTG mode...")
    if not unlocker.start_scrcpy_otg():
        print("Failed to start scrcpy")
        return False
    
    print("2. Please ensure:")
    print("   - The scrcpy window is visible and not minimized")
    print("   - The device screen is showing the lock pattern")
    print("   - You can see the mouse cursor on the device screen")
    print()
    
    input("Press Enter when ready to test pattern unlock...")
    
    print("3. Testing pattern unlock with pattern: 1,2,5,6,9")
    pattern = "1,2,5,6,9"
    
    success = unlocker.draw_pattern(pattern)
    
    if success:
        print("✓ Pattern drawing completed successfully!")
        print("Check if the device unlocked.")
    else:
        print("✗ Pattern drawing failed")
    
    print("4. Cleaning up...")
    unlocker.cleanup()
    
    return success

if __name__ == "__main__":
    try:
        test_mouse_control()
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    except Exception as e:
        print(f"Test failed with error: {e}")
