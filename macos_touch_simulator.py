#!/usr/bin/env python3
"""
macOS Touch Simulator for scrcpy pattern unlock.
Uses native macOS APIs to simulate trackpad input.
"""

import subprocess
import time
from typing import List, Tuple, Optional

class MacOSTouchSimulator:
    """Simulates touch input using macOS native methods."""
    
    def __init__(self):
        """Initialize the touch simulator."""
        self.check_accessibility_permissions()
    
    def check_accessibility_permissions(self):
        """Check if accessibility permissions are granted."""
        print("Note: This script may require accessibility permissions.")
        print("If prompted, please grant accessibility access in System Preferences > Security & Privacy > Privacy > Accessibility")
    
    def simulate_click_and_drag(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 0.5):
        """
        Simulate a click and drag gesture using AppleScript.
        
        Args:
            start_x, start_y: Starting coordinates
            end_x, end_y: Ending coordinates  
            duration: Duration of the drag in seconds
        """
        applescript = f'''
        tell application "System Events"
            -- Move to start position and click down
            set startPos to {{{start_x}, {start_y}}}
            set endPos to {{{end_x}, {end_y}}}
            
            -- Simulate mouse down at start position
            tell application "System Events" to click at startPos
            delay 0.1
            
            -- Simulate drag to end position
            tell application "System Events"
                set mouseLocation to startPos
                repeat with i from 1 to 10
                    set progress to i / 10
                    set currentX to (item 1 of startPos) + progress * ((item 1 of endPos) - (item 1 of startPos))
                    set currentY to (item 2 of startPos) + progress * ((item 2 of endPos) - (item 2 of startPos))
                    set mouseLocation to {{currentX, currentY}}
                    delay {duration / 10}
                end repeat
            end tell
        end tell
        '''
        
        try:
            subprocess.run(['osascript', '-e', applescript], check=True, capture_output=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"AppleScript error: {e}")
            return False
    
    def simulate_trackpad_drag(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 0.5):
        """
        Alternative method using cliclick tool if available.
        """
        try:
            # Check if cliclick is available
            subprocess.run(['which', 'cliclick'], check=True, capture_output=True)
            
            # Use cliclick for more precise control
            cmd = [
                'cliclick', 
                f'dd:{start_x},{start_y}',  # drag down (start drag)
                f'du:{end_x},{end_y}'       # drag up (end drag)
            ]
            
            subprocess.run(cmd, check=True, capture_output=True)
            return True
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("cliclick not available, falling back to AppleScript")
            return self.simulate_click_and_drag(start_x, start_y, end_x, end_y, duration)
    
    def draw_pattern_line(self, points: List[Tuple[int, int]], duration_per_segment: float = 0.3):
        """
        Draw a continuous line through multiple points.
        
        Args:
            points: List of (x, y) coordinates to draw through
            duration_per_segment: Time to spend on each line segment
        """
        if len(points) < 2:
            print("Need at least 2 points to draw a line")
            return False
        
        print(f"Drawing pattern through {len(points)} points...")
        
        # Start with first point
        start_point = points[0]
        print(f"Starting at: {start_point}")
        
        # Draw lines to each subsequent point
        for i in range(1, len(points)):
            end_point = points[i]
            print(f"Drawing line to: {end_point}")
            
            success = self.simulate_trackpad_drag(
                start_point[0], start_point[1],
                end_point[0], end_point[1],
                duration_per_segment
            )
            
            if not success:
                print(f"Failed to draw line to point {i}")
                return False
            
            start_point = end_point
            time.sleep(0.1)  # Small pause between segments
        
        print("Pattern drawing completed")
        return True

def install_cliclick():
    """Install cliclick using Homebrew if available."""
    print("Installing cliclick for better touch simulation...")
    try:
        subprocess.run(['brew', 'install', 'cliclick'], check=True)
        print("cliclick installed successfully!")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Homebrew not available or cliclick installation failed")
        print("You can install cliclick manually from: https://github.com/BlueM/cliclick")
        return False

if __name__ == "__main__":
    # Test the touch simulator
    simulator = MacOSTouchSimulator()
    
    print("Testing macOS touch simulation...")
    print("This will draw a small square on your screen in 3 seconds...")
    time.sleep(3)
    
    # Draw a test square
    center_x, center_y = 720, 450  # Screen center
    size = 50
    
    test_points = [
        (center_x - size, center_y - size),  # Top-left
        (center_x + size, center_y - size),  # Top-right
        (center_x + size, center_y + size),  # Bottom-right
        (center_x - size, center_y + size),  # Bottom-left
        (center_x - size, center_y - size),  # Back to start
    ]
    
    success = simulator.draw_pattern_line(test_points, duration_per_segment=0.5)
    
    if success:
        print("✓ Touch simulation test completed!")
    else:
        print("✗ Touch simulation test failed")
