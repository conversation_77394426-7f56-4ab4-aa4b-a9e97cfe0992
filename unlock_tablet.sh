#!/bin/bash
#
# Samsung Galaxy Tab A 8-inch Unlock Script
# 
# This script unlocks your Galaxy Tab A using the pattern 1,2,5,6,9
# via scrcpy --otg mode (no ADB debugging required)
#
# Usage:
#   ./unlock_tablet.sh           # Normal unlock
#   ./unlock_tablet.sh slow      # Slower swipes for better accuracy
#   ./unlock_tablet.sh no-wake   # Don't wake screen first
#   ./unlock_tablet.sh test      # Test coordinates only
#

# Configuration for your Galaxy Tab A
RESOLUTION="800x1280"
PATTERN="1,2,5,6,9"
SCRCPY_PATH="/Users/<USER>/Downloads/scrcpy-macos-aarch64-v3.3.1/scrcpy"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if tablet is connected
check_connection() {
    print_status "Checking if Galaxy Tab A is connected..."
    
    # Try to start scrcpy briefly to check connection
    timeout 5s "$SCRCPY_PATH" --otg --no-video --no-audio > /dev/null 2>&1
    local exit_code=$?
    
    if [ $exit_code -eq 0 ] || [ $exit_code -eq 124 ]; then
        print_success "Galaxy Tab A detected and ready!"
        return 0
    else
        print_error "Galaxy Tab A not detected. Please check:"
        echo "  • USB cable is connected"
        echo "  • Cable supports data transfer (not just charging)"
        echo "  • Tablet is powered on"
        return 1
    fi
}

# Function to show usage
show_usage() {
    echo "Samsung Galaxy Tab A 8-inch Unlock Script"
    echo "========================================"
    echo ""
    echo "Usage:"
    echo "  $0                # Normal unlock (default)"
    echo "  $0 slow           # Slower swipes for better accuracy"
    echo "  $0 no-wake        # Don't wake screen first"
    echo "  $0 test           # Test coordinates only"
    echo "  $0 help           # Show this help"
    echo ""
    echo "Configuration:"
    echo "  Resolution: $RESOLUTION"
    echo "  Pattern: $PATTERN"
    echo "  scrcpy: $SCRCPY_PATH"
    echo ""
}

# Function to test coordinates
test_coordinates() {
    print_status "Testing pattern coordinates for Galaxy Tab A..."
    python3 "$SCRIPT_DIR/test_galaxy_tab_a.py"
}

# Function to unlock with different modes
unlock_tablet() {
    local mode="$1"
    local extra_args=""
    
    case "$mode" in
        "slow")
            extra_args="--swipe-duration 1.5"
            print_status "Using slow swipe mode for better accuracy..."
            ;;
        "no-wake")
            extra_args="--no-wake"
            print_status "Skipping screen wake-up..."
            ;;
        *)
            print_status "Using normal unlock mode..."
            ;;
    esac
    
    print_status "Starting unlock sequence..."
    print_status "Make sure your Galaxy Tab A is showing the lock screen"
    
    # Run the unlock command
    python3 "$SCRIPT_DIR/pattern_unlock.py" \
        --resolution "$RESOLUTION" \
        --pattern "$PATTERN" \
        --scrcpy-path "$SCRCPY_PATH" \
        $extra_args
    
    local exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        print_success "Unlock sequence completed! 🎉"
        print_status "Your Galaxy Tab A should now be unlocked"
    else
        print_error "Unlock sequence failed!"
        echo ""
        echo "Troubleshooting tips:"
        echo "  • Try: $0 slow (for slower, more accurate swipes)"
        echo "  • Ensure tablet is showing lock screen"
        echo "  • Check USB connection"
        echo "  • Verify pattern is correct: $PATTERN"
    fi
    
    return $exit_code
}

# Main script logic
main() {
    local command="${1:-unlock}"
    
    case "$command" in
        "help" | "-h" | "--help")
            show_usage
            exit 0
            ;;
        "test")
            test_coordinates
            exit 0
            ;;
        "slow" | "no-wake" | "unlock")
            # Check if required files exist
            if [ ! -f "$SCRIPT_DIR/pattern_unlock.py" ]; then
                print_error "pattern_unlock.py not found in $SCRIPT_DIR"
                exit 1
            fi
            
            if [ ! -f "$SCRCPY_PATH" ]; then
                print_error "scrcpy not found at: $SCRCPY_PATH"
                exit 1
            fi
            
            # Check connection
            if ! check_connection; then
                exit 1
            fi
            
            # Perform unlock
            unlock_tablet "$command"
            ;;
        *)
            print_error "Unknown command: $command"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
