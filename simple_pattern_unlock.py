#!/usr/bin/env python3
"""
Simple pattern unlock using manual positioning and basic mouse control.
This approach asks the user to manually position the scrcpy window and uses simple mouse automation.
"""

import argparse
import subprocess
import time
import sys
from typing import List, Tuple, Optional

# Try to import pyautogui, but make it optional
try:
    import pyautogui
    PYAUTOGUI_AVAILABLE = True
    # Disable failsafe for automation
    pyautogui.FAILSAFE = False
except ImportError:
    PYAUTOGUI_AVAILABLE = False
    print("PyAutoGUI not available. Please install with: pip install pyautogui")

class SimplePatternUnlocker:
    """Simple pattern unlocker with manual window positioning."""
    
    def __init__(self, resolution: str, scrcpy_path: str = "scrcpy"):
        """Initialize the pattern unlocker."""
        self.scrcpy_path = scrcpy_path
        self.width, self.height = self._parse_resolution(resolution)
        self.scrcpy_process: Optional[subprocess.Popen] = None
        
        # These will be set by user input
        self.window_x = 0
        self.window_y = 0
        self.window_width = 0
        self.window_height = 0
        
        # Standard Android pattern grid positions (3x3 grid)
        self.pattern_grid = self._calculate_pattern_grid()
    
    def _parse_resolution(self, resolution: str) -> Tuple[int, int]:
        """Parse resolution string into width and height."""
        try:
            width, height = map(int, resolution.split('x'))
            return width, height
        except ValueError:
            raise ValueError(f"Invalid resolution format: {resolution}. Use WIDTHxHEIGHT format.")
    
    def _calculate_pattern_grid(self) -> List[List[Tuple[int, int]]]:
        """Calculate the 3x3 pattern grid coordinates based on screen resolution."""
        # Pattern grid ratios for Samsung devices
        grid_start_x_ratio = 0.2    # 20% from left edge
        grid_end_x_ratio = 0.8      # 80% from left edge
        grid_start_y_ratio = 0.4    # 40% from top edge  
        grid_end_y_ratio = 0.75     # 75% from top edge
        
        grid_start_x = int(self.width * grid_start_x_ratio)
        grid_end_x = int(self.width * grid_end_x_ratio)
        grid_start_y = int(self.height * grid_start_y_ratio)
        grid_end_y = int(self.height * grid_end_y_ratio)
        
        # Calculate 3x3 grid positions
        grid = []
        for row in range(3):
            grid_row = []
            for col in range(3):
                x = grid_start_x + (grid_end_x - grid_start_x) * col // 2
                y = grid_start_y + (grid_end_y - grid_start_y) * row // 2
                grid_row.append((x, y))
            grid.append(grid_row)
        
        return grid
    
    def get_pattern_coordinates(self, pattern: str) -> List[Tuple[int, int]]:
        """Convert pattern string to list of device coordinates."""
        try:
            pattern_numbers = [int(x.strip()) for x in pattern.split(',')]
        except ValueError:
            raise ValueError(f"Invalid pattern format: {pattern}. Use comma-separated numbers 1-9.")
        
        coordinates = []
        for num in pattern_numbers:
            if num < 1 or num > 9:
                raise ValueError(f"Pattern number {num} out of range. Use numbers 1-9.")
            
            # Convert number to grid position (1-9 maps to 3x3 grid)
            row = (num - 1) // 3
            col = (num - 1) % 3
            coordinates.append(self.pattern_grid[row][col])
        
        return coordinates
    
    def start_scrcpy_otg(self) -> bool:
        """Start scrcpy in OTG mode."""
        try:
            print("Starting scrcpy in OTG mode...")
            cmd = [self.scrcpy_path, "--otg"]
            
            self.scrcpy_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give scrcpy time to start
            time.sleep(3)
            
            # Check if process is still running
            if self.scrcpy_process.poll() is not None:
                stdout, stderr = self.scrcpy_process.communicate()
                print("Failed to start scrcpy OTG mode")
                print(f"Error output: {stderr}")
                return False
            
            print("✓ scrcpy OTG mode started successfully")
            print("You should see the device screen in a window")
            return True
            
        except Exception as e:
            print(f"Error starting scrcpy: {e}")
            return False
    
    def get_window_position_from_user(self):
        """Ask user to manually specify the scrcpy window position and size."""
        print("\n" + "="*60)
        print("WINDOW POSITIONING SETUP")
        print("="*60)
        print("We need to know where your scrcpy window is positioned.")
        print("Please look at your scrcpy window and estimate its position and size.")
        print()
        
        if PYAUTOGUI_AVAILABLE:
            current_x, current_y = pyautogui.position()
            print(f"Your current mouse position: ({current_x}, {current_y})")
            print("You can move your mouse to help estimate coordinates.")
            print()
        
        print("Please enter the approximate values:")
        
        try:
            self.window_x = int(input("Window X position (left edge): "))
            self.window_y = int(input("Window Y position (top edge): "))
            self.window_width = int(input("Window width: "))
            self.window_height = int(input("Window height: "))
            
            print(f"\nWindow position set to: ({self.window_x}, {self.window_y})")
            print(f"Window size set to: {self.window_width} x {self.window_height}")
            
        except ValueError:
            print("Invalid input. Using default values.")
            # Default values for typical scrcpy window
            self.window_x = 500
            self.window_y = 100
            self.window_width = 400
            self.window_height = 700
    
    def map_device_to_window_coordinates(self, device_coords: List[Tuple[int, int]]) -> List[Tuple[int, int]]:
        """Map device coordinates to window coordinates."""
        window_coords = []
        
        for device_x, device_y in device_coords:
            # Scale device coordinates to window coordinates
            scale_x = self.window_width / self.width
            scale_y = self.window_height / self.height
            
            window_x = self.window_x + int(device_x * scale_x)
            window_y = self.window_y + int(device_y * scale_y)
            
            window_coords.append((window_x, window_y))
        
        return window_coords
    
    def draw_pattern_with_mouse(self, coordinates: List[Tuple[int, int]], duration: float = 2.0) -> bool:
        """Draw the pattern using mouse automation."""
        if not PYAUTOGUI_AVAILABLE:
            print("PyAutoGUI not available for mouse control")
            return False
        
        if len(coordinates) < 2:
            print("Need at least 2 points to draw a pattern")
            return False
        
        try:
            print(f"Drawing pattern through {len(coordinates)} points...")
            
            # Move to first point and press down
            start_x, start_y = coordinates[0]
            print(f"Starting at: ({start_x}, {start_y})")
            
            pyautogui.moveTo(start_x, start_y, duration=0.5)
            time.sleep(0.2)
            pyautogui.mouseDown()
            
            # Draw lines to each subsequent point
            segment_duration = duration / (len(coordinates) - 1)
            
            for i in range(1, len(coordinates)):
                end_x, end_y = coordinates[i]
                print(f"Drawing to: ({end_x}, {end_y})")
                pyautogui.moveTo(end_x, end_y, duration=segment_duration)
                time.sleep(0.1)
            
            # Release mouse
            pyautogui.mouseUp()
            
            print("✓ Pattern drawing completed!")
            return True
            
        except Exception as e:
            print(f"Error drawing pattern: {e}")
            return False
    
    def unlock_pattern(self, pattern: str, swipe_duration: float = 2.0) -> bool:
        """Perform the unlock pattern."""
        try:
            print(f"\nDrawing unlock pattern: {pattern}")
            
            # Get device coordinates for the pattern
            device_coords = self.get_pattern_coordinates(pattern)
            print(f"Device coordinates: {device_coords}")
            
            # Map to window coordinates
            window_coords = self.map_device_to_window_coordinates(device_coords)
            print(f"Window coordinates: {window_coords}")
            
            # Draw the pattern
            success = self.draw_pattern_with_mouse(window_coords, swipe_duration)
            
            if success:
                print("✓ Pattern unlock completed!")
                print("Check if the device unlocked.")
                return True
            else:
                print("✗ Pattern unlock failed!")
                return False
                
        except Exception as e:
            print(f"Error during pattern unlock: {e}")
            return False
    
    def cleanup(self) -> None:
        """Clean up resources."""
        if self.scrcpy_process and self.scrcpy_process.poll() is None:
            print("Terminating scrcpy process...")
            self.scrcpy_process.terminate()
            try:
                self.scrcpy_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                print("Force killing scrcpy process...")
                self.scrcpy_process.kill()
            self.scrcpy_process = None

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Simple Samsung pattern unlock")
    parser.add_argument("--resolution", required=True, help="Screen resolution (e.g., 1080x2340)")
    parser.add_argument("--pattern", required=True, help="Unlock pattern (e.g., 1,2,5,6,9)")
    parser.add_argument("--scrcpy-path", default="scrcpy", help="Path to scrcpy executable")
    parser.add_argument("--swipe-duration", type=float, default=2.0, help="Duration for pattern drawing")
    
    args = parser.parse_args()
    
    if not PYAUTOGUI_AVAILABLE:
        print("Error: PyAutoGUI is required. Install with: pip install pyautogui")
        return 1
    
    unlocker = SimplePatternUnlocker(args.resolution, args.scrcpy_path)
    
    try:
        # Start scrcpy
        if not unlocker.start_scrcpy_otg():
            print("Failed to start scrcpy")
            return 1
        
        # Get window position from user
        unlocker.get_window_position_from_user()
        
        # Confirm ready to proceed
        input("\nPress Enter when you're ready to draw the pattern...")
        
        # Perform unlock
        success = unlocker.unlock_pattern(args.pattern, args.swipe_duration)
        
        return 0 if success else 1
            
    except KeyboardInterrupt:
        print("\nInterrupted by user")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1
    finally:
        unlocker.cleanup()

if __name__ == "__main__":
    sys.exit(main())
